<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="报警详情"
    width="650px"
    center
    custom-class="alarm-detail-dialog"
  >
    <div v-if="detailData" class="alarm-detail-info">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="设备名称" :span="2">{{ detailData.deviceName }}</el-descriptions-item>
        <el-descriptions-item label="测点名称">{{ detailData.measurementName }}</el-descriptions-item>
        <el-descriptions-item label="定义名称">{{ detailData.definitionName }}</el-descriptions-item>
        <el-descriptions-item label="报警时间" :span="2">{{ detailData.warnTime }}</el-descriptions-item>
        <el-descriptions-item label="报警类型">{{ detailData.warnType }}</el-descriptions-item>
        <el-descriptions-item label="报警级别">
          <el-tag :type="getAlarmLevelTag(detailData.warnLevel)" effect="dark">
            {{ getAlarmLevelText(detailData.warnLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="处理状态">
          <el-tag :type="detailData.isSolved === 1 ? 'success' : 'info'" effect="plain">
            {{ detailData.isSolved === 1 ? '已处理' : '未处理' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="标准值">{{ detailData.normalValue || '-' }}</el-descriptions-item>
        <el-descriptions-item label="报警值">{{ detailData.warnValue || '-' }}</el-descriptions-item>
        <el-descriptions-item label="报警消息" :span="2">{{ detailData.warnMsg }}</el-descriptions-item>
        <el-descriptions-item label="设备ID">{{ detailData.deviceId || '-' }}</el-descriptions-item>
        <el-descriptions-item label="测点ID">{{ detailData.measurementId || '-' }}</el-descriptions-item>
        <el-descriptions-item v-if="detailData.isSolved === 1" label="处理时间">
          {{ detailData.confirmTime }}
        </el-descriptions-item>
        <el-descriptions-item label="持续时间">
          <span v-if="detailData.isSolved === 1">{{ detailData.duration }}</span>
          <span v-else>{{ formatDuration(detailData.warnTime) }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <div v-if="detailData.measurementDefinition" class="measurement-definition-info">
        <h4>测点定义详情</h4>
        <el-descriptions :column="2" border size="small">
          <el-descriptions-item label="测点定义名称" :span="2">{{ detailData.measurementDefinition.measureDefineName }}</el-descriptions-item>
          <el-descriptions-item label="频率范围">{{ detailData.measurementDefinition.lowerLimitFrequency }}-{{ detailData.measurementDefinition.upperLimitFrequency }}HZ</el-descriptions-item>
          <el-descriptions-item label="谱线数">{{ detailData.measurementDefinition.numberOfSpectralLines }}线</el-descriptions-item>
          <el-descriptions-item label="一级报警值">{{ detailData.measurementDefinition.firstAlarmValue || '-' }}</el-descriptions-item>
          <el-descriptions-item label="二级报警值">{{ detailData.measurementDefinition.secondAlarmValue || '-' }}</el-descriptions-item>
          <el-descriptions-item label="三级报警值">{{ detailData.measurementDefinition.thirdAlarmValue || '-' }}</el-descriptions-item>
          <el-descriptions-item label="包络频率" v-if="detailData.measurementDefinition.envelopeFrequency">{{ detailData.measurementDefinition.envelopeFrequency }}</el-descriptions-item>
          <el-descriptions-item label="窗函数ID" v-if="detailData.measurementDefinition.windowsFunctionId">{{ detailData.measurementDefinition.windowsFunctionId }}</el-descriptions-item>
          <el-descriptions-item label="FFT类型ID" v-if="detailData.measurementDefinition.fastFourierTransformTypeId">{{ detailData.measurementDefinition.fastFourierTransformTypeId }}</el-descriptions-item>
          <el-descriptions-item label="平均类型" v-if="detailData.measurementDefinition.averageType">{{ detailData.measurementDefinition.averageType }}</el-descriptions-item>
          <el-descriptions-item label="平均值" v-if="detailData.measurementDefinition.averageValue">{{ detailData.measurementDefinition.averageValue }}</el-descriptions-item>
          <el-descriptions-item label="数据重叠率" v-if="detailData.measurementDefinition.dataOverlapRate">{{ detailData.measurementDefinition.dataOverlapRate }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
      <el-button type="success" @click="goToTripleChart">查看三联图</el-button>
      <el-button
        v-if="showConfirmButton"
        type="primary"
        @click="confirmAlarm">处理报警</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'AlarmDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => ({})
    },
    showConfirmButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      detailData: null
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    detail: {
      handler(val) {
        this.detailData = val
      },
      immediate: true,
      deep: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    closeDialog() {
      this.dialogVisible = false
    },
    confirmAlarm() {
      this.$emit('confirm', this.detailData)
      this.closeDialog()
    },
    getAlarmLevelTag(level) {
      const levelMap = {
        '1': 'warning',  // 一级报警 - 黄色
        '2': 'warning',  // 二级报警 - 橙色
        '3': 'danger'    // 三级报警 - 红色
      };
      return levelMap[level] || 'info';
    },
    getAlarmLevelText(level) {
      const levelMap = {
        '1': '一级报警',
        '2': '二级报警',
        '3': '三级报警'
      };
      return levelMap[level] || '未知级别';
    },
    formatDuration(startTimeStr) {
      // 计算持续时间
      const startTime = new Date(startTimeStr);
      const now = new Date();
      const diffMs = now - startTime;
      const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      let result = '';
      if (days > 0) result += `${days}天`;
      if (hours > 0) result += `${hours}小时`;
      result += `${minutes}分钟`;
      return result;
    },
    goToTripleChart() {
      // 构建数据对象
      const alarmData = { measurementId : this.detailData.measurementId }
      //存储到vuex
      this.$store.dispatch('dataStore/SET_ALARM_DETAIL_DATA', alarmData)
      // 设置图表类型为三联图
      this.$store.dispatch('chartSwitcher/setActiveChart', 'TrendWaveformSpectrumChart')
      // 跳转到textPage
      this.$router.push({
        path: '/textPage',
        query: {
          fromAlarmDetail: true,  // 标识来源
          chartType: 'TrendWaveformSpectrumChart'
        }
      })
      // 关闭对话框
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-detail-info {
  padding: 10px;

  h4 {
    margin: 20px 0 10px;
    font-weight: bold;
    color: #303133;
    font-size: 14px;
  }

  .measurement-definition-info {
    margin-top: 20px;
  }
}

:deep(.el-descriptions-item__label) {
  width: 100px;
  background-color: #f5f7fa;
  white-space: nowrap;
}

:deep(.el-descriptions-item__content) {
  padding: 8px 10px;
  word-break: break-word;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-tag) {
  font-size: 12px;
}
</style>

<style>
.alarm-detail-dialog {
  margin: 0 auto !important;
}

.alarm-detail-dialog .el-dialog__body {
  padding: 15px 20px;
}

.alarm-detail-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #EBEEF5;
}

.alarm-detail-dialog .el-dialog__footer {
  padding: 15px 20px;
  border-top: 1px solid #EBEEF5;
}
</style>
