<!-- 通道温度图 -->
<template>
  <div class="CTC">
    <div class="top-container">
      <span>{{ treePathText }} 通道温度图</span>
      <!-- 添加时间选择器 -->
      <div class="time-picker-container">
        <el-date-picker
          v-model="startTime"
          type="datetime"
          placeholder="选择开始时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :editable="false"
          :clearable="false"
          :picker-options="pickerOptions"
          @change="handleTimeChange"
        ></el-date-picker>
        <span class="time-separator">至</span>
        <el-date-picker
          v-model="endTime"
          type="datetime"
          placeholder="选择结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :editable="false"
          :clearable="false"
          :picker-options="pickerOptions"
          @change="handleTimeChange"
        ></el-date-picker>
      </div>
    </div>
    <div ref="CT_chart" class="chart-container"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState } from 'vuex'
import { getChannelTemperatureData } from './tempApi'

export default {
  data() {
    return {
      chart: null,  // 存储 echarts 实例
      resizeObserver: null, // 添加 resizeObserver 到 data
      temperatureData: [],
      positions: [],
      startTime: this.getDefaultStartTime(), // 默认开始时间
      endTime: this.getDefaultEndTime(), // 默认结束时间
      selectedPoint: null, // 存储选中的测点信息
      loading: false, // 加载状态
      hasError: false, // 错误状态
      errorMessage: '', // 错误消息
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000);
            picker.$emit('pick', start);
          }
        }, {
          text: '今天',
          onClick(picker) {
            const end = new Date();
            const start = new Date(end.getFullYear(), end.getMonth(), end.getDate());
            picker.$emit('pick', start);
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24);
            picker.$emit('pick', start);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', start);
          }
        }]
      }
    };
  },
  computed: {
    ...mapState('tree', ['treePathText']), // 通过 Vuex 获取路径信息
    selectedNode() {
      const node = this.$store.state.tree.selectedTreeNode;
      if (node) {
        console.log('通道温度图-计算属性获取节点:', {
          id: node.id,
          treeicon: node.treeicon,
          name: node.name
        });
      }
      return node; // 返回整个对象，而不是只返回id
    }
  },
  watch: {
    'selectedNode': {
      async handler(newVal, oldVal) {
        console.log('通道温度图监听到节点变化:', newVal ? {
          id: newVal.id,
          treeicon: newVal.treeicon,
          name: newVal.name
        } : 'null'); // 更详细的日志

        if (newVal && (newVal.treeicon === 'measureDef' || newVal.treeicon === 'waterPump' || newVal.treeicon === 'bearing')) {
          console.log('通道温度图-将处理节点变化:', {
            newId: newVal.id,
            treeicon: newVal.treeicon
          });

          // 确保先初始化图表
          await this.initChart();
          if (this.chart) {
            await this.fetchTemperatureData();
          } else {
            console.error('图表实例未初始化，无法获取数据');
          }
        } else {
          console.log('通道温度图-忽略不相关节点类型:', newVal ? newVal.treeicon : 'null');
        }
      },
      deep: true,
      immediate: true
    },
    // 监听温度数据变化，自动清除错误状态
    temperatureData: {
      handler(newData) {
        if (newData && newData.length > 0 && this.hasError) {
          console.log('检测到有新的温度数据，自动清除错误状态');
          this.hasError = false;
          this.errorMessage = '';
        }
      },
      deep: false
    }
  },
  mounted() {
    console.log('通道温度图-组件挂载');

    this.$nextTick(async () => {
      console.log('通道温度图-nextTick触发，当前选中节点：', this.selectedNode ? {
        id: this.selectedNode.id,
        treeicon: this.selectedNode.treeicon,
        name: this.selectedNode.name
      } : 'null');

      if (this.selectedNode && (this.selectedNode.treeicon === 'measureDef' || this.selectedNode.treeicon === 'device' || this.selectedNode.treeicon === 'waterPump' || this.selectedNode.treeicon === 'bearing')) {
        console.log('通道温度图-挂载时处理有效节点');
        await this.initChart(); // 等待图表初始化完成
        if (this.chart) { // 确保图表实例存在
          console.log('通道温度图-图表初始化成功，开始获取数据');
          await this.fetchTemperatureData();
        } else {
          console.error('通道温度图-图表初始化失败');
        }
      } else {
        console.log('通道温度图-挂载时无有效节点或节点类型不匹配');
      }
    });

    // 创建 ResizeObserver 实例
    this.resizeObserver = new ResizeObserver(() => {
      if (this.chart) {
        this.chart.resize();
      }
    });

    // 监听图表容器
    if (this.$refs.CT_chart) {
      this.resizeObserver.observe(this.$refs.CT_chart);
    } else {
      console.warn('通道温度图-挂载时图表容器不存在');
    }

    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      // 移除事件监听
      this.chart.off('click');
      this.chart.off('datazoom');
      this.chart.off('rendered');

      // 销毁图表实例
      this.chart.dispose();
      this.chart = null;
    }

    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 获取默认开始时间（当前时间的前一天）
    getDefaultStartTime() {
      const date = new Date();
      date.setDate(date.getDate() - 1);
      return this.formatDateTime(date);
    },

    // 获取默认结束时间（当前时间）
    getDefaultEndTime() {
      return this.formatDateTime(new Date());
    },

    // 格式化日期时间为 YYYY-MM-DD HH:mm:ss
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 处理时间变化
    handleTimeChange() {
      if (this.startTime && this.endTime) {
        // 检查开始时间是否晚于结束时间
        if (new Date(this.startTime) > new Date(this.endTime)) {
          this.$message.error('开始时间不能晚于结束时间');
          // 重置为当前值的前后一天
          this.startTime = this.getDefaultStartTime();
          this.endTime = this.getDefaultEndTime();
          return;
        }

        // 在重新获取数据前清除错误状态
        this.hasError = false;
        this.errorMessage = '';

        // 重新获取数据
        this.fetchTemperatureData();
      }
    },

    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },

    initChart() {
      if (this.chart) return; // 防止重复初始化

      try {
        if (!this.$refs.CT_chart) {
          console.error('图表容器元素不存在');
          return;
        }

        // 确保容器具有尺寸
        const chartDom = this.$refs.CT_chart;
        if (chartDom.offsetHeight === 0 || chartDom.offsetWidth === 0) {
          console.warn('图表容器尺寸为0，可能导致图表不显示');
        }

        this.chart = echarts.init(this.$refs.CT_chart);
        console.log('图表初始化完成', this.chart);

        // 移除点击事件绑定
        // this.chart.on('click', this.handleChartClick);
        this.chart.on('datazoom', this.handleDataZoom);

        // 添加渲染完成事件
        this.chart.on('rendered', () => {
          if (this.hasError && this.temperatureData && this.temperatureData.length > 0) {
            console.log('图表渲染完成，检测到有数据但处于错误状态，尝试清除错误');
            this.hasError = false;
            this.errorMessage = '';

            if (this.chart) {
              this.chart.setOption({
                graphic: []
              }, false);
            }
          }
        });
      } catch (error) {
        console.error('初始化图表时出错:', error);
      }
    },

    async fetchTemperatureData() {
      try {
        this.loading = true;
        this.hasError = false;  // 重置错误状态
        this.errorMessage = '';  // 清空错误消息

        const node = this.selectedNode;
        if (!node || !node.id) {
          console.log('没有选中节点或ID无效');
          this.showError('未选择有效节点，无法获取数据');
          return;
        }

        console.log('准备获取温度数据，节点类型:', node.treeicon, '节点ID:', node.id);

        // 统一参数结构，确保所有请求都包含ids参数
        const params = {
          ids: node.id,
          time: `${this.startTime},${this.endTime}`,
          type: 'temperature',
          originType: 'all'
        };

        // 根据节点类型添加特定参数
        if (node.treeicon === 'bearing' || node.treeicon === 'waterPump') {
          params.ids = node.id;
        }

        console.log('获取温度数据参数:', params);

        const response = await getChannelTemperatureData(params);
        console.log('获取温度数据成功:', response);

        // 检查响应中是否包含所需的数据结构
        if (response && response.trendData && response.trendData.temperature) {
          // 获取温度数据对象
          const tempData = response.trendData.temperature;

          // 获取第一个键（通常是通道ID，如3124）
          const channelId = Object.keys(tempData)[0];

          if (channelId && tempData[channelId]) {
            // 直接获取x（时间）和y（温度）数据
            const xData = tempData[channelId].x;
            const yData = tempData[channelId].y;

            if (xData && yData && xData.length > 0 && yData.length > 0) {
              // 处理数据，只过滤null和NaN值
              const validPairs = [];
              for (let i = 0; i < xData.length; i++) {
                const temp = yData[i];
                const numValue = parseFloat(temp);
                // 只过滤null和NaN值，-50是有效温度值
                if (temp !== null && !isNaN(numValue)) {
                  validPairs.push({
                    position: xData[i],
                    temperature: numValue
                  });
                }
              }

              if (validPairs.length > 0) {
                // 如果有有效数据，更新图表
                this.positions = validPairs.map(pair => pair.position);
                this.temperatureData = validPairs.map(pair => pair.temperature);

                // 先将loading设为false，防止在updateChart时数据状态不一致
                this.loading = false;

                // 确保清除错误状态
                this.hasError = false;
                this.errorMessage = '';

                // 确保this.positions和this.temperatureData有效后更新图表
                if (this.positions.length > 0 && this.temperatureData.length > 0) {
                  this.updateChart();
                } else {
                  this.showError('数据处理后无有效数据可显示');
                }
                return;
              } else {
                console.warn('没有有效的温度数据点');
                this.showError('所选时间范围内没有有效的温度数据');
              }
            } else {
              this.showError('返回的温度数据为空');
            }
          } else {
            this.showError('通道温度图暂无数据');
          }
        } else {
          // 如果到达这里，说明数据结构不符合预期或者没有有效数据
          console.warn('温度数据结构不符合预期或没有有效数据');
          this.showError('数据加载失败，请联系管理员');
        }
      } catch (error) {
        console.error('获取温度数据失败:', error);
        this.showError('数据加载失败，请联系管理员');
      } finally {
        this.loading = false;
      }
    },

    // 显示错误信息
    showError(message) {
      this.hasError = true;
      this.errorMessage = message;
      this.temperatureData = [];
      this.positions = [];
      // this.$message.info(message);

      // 如果有图表实例，显示错误状态
      if (this.chart) {
        this.showErrorInChart(message);
      }
    },

    // 清除错误状态并重置图表
    clearError() {
      if (this.hasError) {
        this.hasError = false;
        this.errorMessage = '';
        // 如果有图表实例但没有数据，可能需要重新获取数据
        if (this.chart && (!this.temperatureData || this.temperatureData.length === 0)) {
          this.fetchTemperatureData();
        } else if (this.chart) {
          // 如果已有数据，直接更新图表
          this.updateChart();
        }
      }
    },

    // 在图表中显示错误信息
    showErrorInChart(message) {
      if (!this.chart) return;

      // 先清空图表内容
      this.chart.clear();

      const option = {
        title: {
          text: '通道温度图',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          },
          subtext: `${this.startTime} 至 ${this.endTime}`,
          subtextStyle: {
            fontSize: 14,
            color: '#666'
          }
        },
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: message || '数据加载失败，请联系管理员',
              fontSize: 18,
              fontWeight: 'bold',
              fill: '#666666',
              width: 400,
              overflow: 'break'
            }
          }
        ],
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        series: []
      };

      // 使用 true 参数确保完全替换之前的配置
      this.chart.setOption(option, true);
    },

    updateChart() {
      if (!this.chart) {
        console.error('图表实例不存在，无法更新图表');
        return;
      }

      try {
        // 检查是否有错误状态，并且是否有有效数据
        if (this.hasError) {
          // 如果有错误但也有数据，尝试正常显示数据
          if (this.temperatureData && this.temperatureData.length > 0 &&
              this.positions && this.positions.length > 0) {
            // 有数据时清除错误状态，继续正常显示
            this.hasError = false;
            this.errorMessage = '';
            console.log('检测到有效数据，清除错误状态');
          } else {
            // 没有数据时显示错误信息
            this.showErrorInChart(this.errorMessage);
            return;
          }
        }

        // 再次确认有有效数据
        if (!this.temperatureData || !this.positions ||
            this.temperatureData.length === 0 || this.positions.length === 0) {
          console.warn('没有有效的图表数据');
          return;
        }

        // 过滤掉无效数据后再计算范围，-50是有效值
        const validTemps = this.temperatureData.filter(temp =>
          !isNaN(temp) && temp !== null
        );

        if (validTemps.length === 0) {
          console.warn('没有有效的温度数据点可以显示');
          this.showError('无有效数据可显示');
          return;
        }

        // 调整Y轴范围以适应数据
        const minTemp = Math.min(...validTemps);
        const maxTemp = Math.max(...validTemps);
        // 添加一些余量，使图表更美观
        const yMin = Math.floor(minTemp > 0 ? minTemp * 0.9 : minTemp * 1.1);
        const yMax = Math.ceil(maxTemp > 0 ? maxTemp * 1.1 : maxTemp * 0.9);

        // 移除所有graphic元素（错误信息图标和文字）
        this.chart.setOption({
          graphic: []
        }, false);

        // 清空图表内容，确保完全重绘
        this.chart.clear();

        // 统一温度值格式化函数，确保所有地方格式一致
        const formatTemperature = function(value) {
          return parseFloat(value).toFixed(4) + '°C';
        };

        const option = {
          title: {
            text: '通道温度图',
            left: 'center',
            textStyle: {
              fontSize: 16,
              fontWeight: 'bold',
              color: '#333'
            },
            subtext: `${this.startTime} 至 ${this.endTime}`,
            subtextStyle: {
              fontSize: 14,
              color: '#666'
            }
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(50,50,50,0.9)',
            borderColor: '#3498db',
            borderWidth: 2,
            textStyle: {
              color: '#fff',
              fontSize: 14,
              fontWeight: 'bold'
            },
            padding: [10, 15],
            axisPointer: {
              type: 'line',
              lineStyle: {
                color: '#3498db',
                width: 2,
                type: 'dashed'
              }
            },
            formatter: function (params) {
              // 使用统一的温度格式化函数
              const tempValue = formatTemperature(params[0].data);
              return '<div style="font-size:14px;font-weight:bold;margin-bottom:5px;">' +
                     params[0].axisValue + '</div>' +
                     params[0].marker + '<span style="color:#3498db;font-size:16px;font-weight:bold">温度: ' +
                     tempValue + '</span>';
            },
            extraCssText: 'box-shadow: 0 4px 8px rgba(0,0,0,0.3); border-radius: 4px;'
          },
          grid: {
            left: '10%',
            right: '10%',
            bottom: '15%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            name: '时间',
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 'bold',
              padding: [5, 0, 0, 0]
            },
            boundaryGap: false,
            data: this.positions,
            axisLabel: {
              interval: 'auto', // 自动调整标签显示
              rotate: 45,      // 旋转标签，节省空间
              color: '#333',   // 更深的文字颜色
              fontSize: 14,    // 增大字体
              fontWeight: 'normal',
              margin: 14,
              formatter: function (value) {
                // 如果是时间戳格式，显示简短形式
                if (typeof value === 'string' && value.includes('-')) {
                  // 提取时间部分
                  const timePart = value.split(' ')[1];
                  return timePart || value;
                }
                return value;
              }
            },
            axisLine: {
              lineStyle: {
                color: '#666',
                width: 1.5
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '温度 (°C)',
            nameTextStyle: {
              fontSize: 14,
              fontWeight: 'bold',
              padding: [0, 0, 5, 0],
              color: '#333'
            },
            min: yMin,
            max: yMax,
            axisLabel: {
              formatter: function(value) {
                // 使用统一的温度格式化函数
                return formatTemperature(value);
              },
              color: '#333',
              fontSize: 14,
              fontWeight: 'bold',
              margin: 12
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
                type: 'dashed',
                width: 1.5
              }
            }
          },
          // 添加visualMap组件，使颜色根据温度值变化
          visualMap: {
            show: false,
            type: 'continuous',
            seriesIndex: 0,
            dimension: 1,
            min: yMin,
            max: yMax,
            text: ['高', '低'],
            calculable: true,
            inRange: {
              color: ['#3498db', '#9b59b6', '#e74c3c']
            },
            orient: 'horizontal',
            left: 'center',
            bottom: 0,
            formatter: function(value) {
              return formatTemperature(value);
            }
          },
          dataZoom: [
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100,
              filterMode: 'none'
            }
          ],
          series: [{
            name: '温度',
            type: 'line',
            data: this.temperatureData,
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            sampling: 'average', // 大数据采样
            lineStyle: {
              width: 4,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
              shadowBlur: 10,
              shadowOffsetY: 5
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2
            },
            markPoint: {
              silent: true,
              symbol: 'pin',
              symbolSize: 50,
              itemStyle: {
                color: '#e74c3c',
                borderColor: '#fff',
                borderWidth: 2,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 5
              },
              data: [
                { type: 'max', name: '最大值' },
                { type: 'min', name: '最小值' }
              ],
              label: {
                formatter: function(params) {
                  // 使用统一的温度格式化函数
                  return params.name + ': ' + formatTemperature(params.value);
                },
                color: '#fff',
                fontSize: 14,
                fontWeight: 'bold',
                backgroundColor: 'rgba(52,152,219,0.8)',
                padding: [4, 8],
                borderRadius: 4
              },
            },
            markLine: {
              symbol: 'none',
              lineStyle: {
                color: '#e67e22',
                width: 2.5,
                type: 'dashed'
              },
              data: [
                { type: 'average', name: '平均值' }
              ],
              label: {
                formatter: function(params) {
                  // 使用统一的温度格式化函数
                  return '平均: ' + formatTemperature(params.value);
                },
                color: '#e67e22',
                fontSize: 14,
                fontWeight: 'bold',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                padding: [5, 10],
                borderRadius: 4,
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 3
              }
            }
          }]
        };

        this.chart.setOption(option);
        console.log('图表更新完成');

        // 确保更新后进行一次重绘
        this.chart.resize();
      } catch (error) {
        console.error('更新图表时出错:', error);
      }
    },

    // 处理数据缩放事件
    handleDataZoom() {
      if (this.selectedPoint && !this.hasError) {
        this.updateMark();
      }
    },

    // 找到最近的数据点
    findNearestPoint(xIndex) {
      if (!this.positions || !this.positions.length) return null;

      // 将浮点索引转为整数索引
      const index = Math.round(xIndex);

      // 确保索引在有效范围内
      if (index >= 0 && index < this.positions.length) {
        return {
          index: index,
          xValue: this.positions[index],
          yValue: this.temperatureData[index]
        };
      }
      return null;
    }
  }
}
</script>

<style scoped lang="scss">
.CTC {
  width: 100%;
  height: 100%;
  margin: 0 !important;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  border-radius: 5px;
  background-color: #fff; /* 添加背景色以便于调试 */
}

.top-container {
  width: 100%;
  height: 50px; /* 增加高度以容纳时间选择器 */
  padding: 3px 10px;
  background-color: #f5f5f5;
  font-size: 14px;
  text-align: left;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time-picker-container {
  display: flex;
  align-items: center;
}

.time-separator {
  margin: 0 8px;
  color: #666;
}

.chart-container {
  width: 100%;
  height: calc(100% - 50px); /* 调整高度以适应更高的顶部容器 */
  background-color: #f9f9f9; /* 添加背景色以便于调试 */
  border: 1px solid #eee; /* 添加边框以便于调试 */
}

/* 修改Element UI日期选择器样式，使其更紧凑 */
:deep(.el-date-editor.el-input) {
  width: 180px;
}

:deep(.el-input__inner) {
  height: 32px;
  line-height: 32px;
  font-size: 12px;
}

:deep(.el-input__icon) {
  line-height: 32px;
}
</style>
