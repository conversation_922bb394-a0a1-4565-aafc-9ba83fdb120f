<template>
  <div class="qscontainer">
    <!-- 顶部容器，显示路径和选中数据 -->
    <div class="top-container">{{ treePathText }} 趋势图 {{ currentXValue }} , {{ currentYValue }} m/s²

      <button class="btn" style="margin-left: auto; box-sizing: border-box; border-radius: 8px; width: 30px;height: 30px;" @click="handleToggle">
              <!-- 根据放大/收缩状态切换不同的图标 -->
              <svg-icon icon-class='unfold' />
      </button>
    </div>
    <!-- 工具栏 -->
    <div class="trend-toolbar">
      <div class="fuction">
        <!-- 现有的按钮 -->
        <button
          class="cursor-btn"
          @click="moveToPreviousPoint"
          title="上一笔"
        >
          上一笔
        </button>
        <button
          class="cursor-btn"
          @click="moveToNextPoint"
          title="下一笔"
        >
          下一笔
        </button>
        <button
          class="cursor-btn"
          @click="downloadChart('trend')"
          title="下载图表"
        >
          下载
        </button>
        <button
          class="cursor-btn"
          @click="resetAxisRange()"
          title="重置缩放"
        >
          重置缩放
        </button>
        <button
          class="cursor-btn"
          @click="showAxisRangeDialog = true"
          title="设置坐标轴范围"
        >
          范围
        </button>
      </div>
      <!-- 添加删除提示 -->
      <div class="delete-tip" v-if="selectedPoint">
        <i class="el-icon-delete"></i> 按 Del 键删除选中点
      </div>
    </div>
    <div ref="chart" class="chart" tabindex="0" @keydown="handleChartKeyDown" >
      <div v-if="!hasData" class="no-data-tip">暂无趋势数据</div>
    </div>

    <!-- 添加坐标轴范围对话框 -->
    <el-dialog
      title="设置坐标轴范围"
      :visible.sync="showAxisRangeDialog"
      width="400px"
      :close-on-click-modal="false"
      :modal="false"
      custom-class="axis-range-dialog"
    >
      <div class="axis-range-form">
        <div class="axis-group">
          <div class="axis-label">时间范围:</div>
          <el-date-picker
            v-model="axisRange.timeRange"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            format="yyyy-MM-dd HH:mm"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 100%;"
            unlink-panels
            :default-time="['00:00:00', '23:59:59']"
          />
        </div>
        <div class="axis-group">
          <div class="axis-label">幅值范围(m/s²):</div>
          <el-input
            v-model="axisRange.yMin"
            placeholder="最小值"
            type="number"
            size="small"
            style="width: 45%; margin-right: 10px;"
          >
            <template slot="prepend">Min</template>
          </el-input>
          <el-input
            v-model="axisRange.yMax"
            placeholder="最大值"
            type="number"
            size="small"
            style="width: 45%;"
          >
            <template slot="prepend">Max</template>
          </el-input>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetAxisRange">重置</el-button>
        <el-button type="primary" @click="applyAxisRange">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState, mapActions} from 'vuex'
import { getTrend, deleteTrend } from './getTrendData';
import ChartZoom from '../ChartZoom'
import ChartKeyboardNavigation from './chartKeyboardNavigation'
import { chartDownloadMixin } from '../mixins/chartDownload'

export default {
  name: 'TrendChart',
  mixins: [chartDownloadMixin],
  props: {
    isExpanded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null,
      chartInstance: null, // 存储 ECharts 实例
      currentXValue: null, // 当前选中的 x 轴值
      currentYValue: null, // 当前选中的 y 轴值
      data: [], // y轴数据
      xAxisData: [], // x轴数据
      zData: [], // z轴数据
      selectedPoint: null, // 存储当前选中的点
      keyboardNavigation: null, // 添加键盘导航实例
      resizeObserver: null, // 添加 resizeObserver 到 data
      chartZoom: null, // 存储图表缩放实例
      showAxisRangeDialog: false, // 坐标轴范围对话框显示状态
      axisRange: {
        timeRange: [], // 用来替代 xMin / xMax
        xMin: null,
        xMax: null,
        yMin: null,
        yMax: null
      },
      defaultAxisRange: {
        xMin: null,
        xMax: null,
        yMin: null,
        yMax: null
      },
      hasData: false // 添加一个标志来跟踪是否有数据
    }
  },
  computed: {
    ...mapState('tree', ['treePathText']), // 通过 Vuex 获取路径信息
    selectedNode() {
      return this.$store.state.tree.selectedTreeNode; // 返回整个对象，而不是只返回id
    }
  },
  watch: {
    isExpanded: {
      immediate: true,
      handler(newVal) {
        // 添加 $nextTick 和空值检查
        this.$nextTick(() => {
          if (this.chart) {
            this.updateChart()
          }
        })
      }
    },

    'selectedNode': {

    async handler(newVal, oldVal) {
        console.log('监听到节点变化:', newVal); // 添加日志
        if (newVal && newVal.treeicon === 'measureDef') {
          console.log('bearing节点变化:', {
            newId: newVal.id,
            treeicon: newVal.treeicon
          });
          await this.reloadChartData();
        }else{
          return
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
      this.$nextTick(async () => {
        if (this.selectedNode && this.selectedNode.treeicon === 'measureDef') {
          await this.initChart(); // 等待图表初始化完成
          if (this.chartInstance) { // 确保图表实例存在
            await this.reloadChartData(); // 使用reloadChartData替代直接调用updateChart
            // 初始化键盘导航
            this.initKeyboardNavigation();
            // 初始化图表缩放功能
            this.chartZoom = new ChartZoom(this.chartInstance);
          }
        }
      })
    // 创建 ResizeObserver 实例
    this.resizeObserver = new ResizeObserver(() => {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    });

    // 监听图表容器
    if (this.$refs.chart) {
      this.resizeObserver.observe(this.$refs.chart);
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    // 清理图表缩放功能
    if (this.chartZoom) {
      this.chartZoom.dispose();
      this.chartZoom = null;
    }
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }
    // 清理键盘导航
    if (this.keyboardNavigation) {
      this.keyboardNavigation.destroy();
      this.keyboardNavigation = null;
    }
    // 移除键盘事件监听
    if (this.$refs.chart) {
      this.$refs.chart.removeEventListener('keydown', this.handleKeyPress);
      this.$refs.chart.removeEventListener('keydown', this.handleChartKeyDown);
    }
  },
  methods: {
    ...mapActions('tree', ['setTreePathText']), // 映射 Vuex 的 action
    // ...mapGetters('tree', ['selectedNode']),
    handleToggle() {
      this.$emit('toggleExpand'); // 触发展开/收起事件
    },
    // 初始化图表
    async initChart() {
      if (!this.$refs.chart) {
        return
      }

      try {
        this.chart = echarts.init(this.$refs.chart)
        this.chartInstance = this.chart  // 保持引用一致
        this.updateChart()
      } catch (error) {
        console.error('初始化图表时出错:', error)
      }
    },
    // 添加键盘导航初始化方法
    initKeyboardNavigation() {
      if (this.keyboardNavigation) {
        this.keyboardNavigation.destroy();
      }
      this.keyboardNavigation = new ChartKeyboardNavigation(this.chartInstance, {
        data: this.data,
        xAxisData: this.xAxisData,
        chartElement: this.$refs.chart,
        onPointChange: ({ xValue, yValue }) => {
          this.currentXValue = xValue;
          this.currentYValue = yValue;
          this.updateMark();
        }
      });
      this.keyboardNavigation.init();

      // 移除之前可能存在的监听器
      this.$refs.chart.removeEventListener('keydown', this.handleKeyPress);

      // 现在只通过handleChartKeyDown处理所有键盘事件
      // 不再添加额外的键盘事件监听器
    },
    // 重新加载图表数据
    async reloadChartData() {
      this.hasData = false; // 默认设置为无数据
      this.data = [] // 清空现有数据
      this.xAxisData = [] // 清空x轴数据
      await this.getData() // 获取新节点的数据
      // 确保图表实例存在
      if (!this.chartInstance && this.$refs.chart) {
        await this.initChart()
      }
      if (this.chartInstance) {
        this.updateChart() // 更新图表配置
        // 更新键盘导航的数据
        if (this.keyboardNavigation) {
          this.keyboardNavigation.updateData(this.data, this.xAxisData);
        }

        // 重新初始化图表缩放功能
        if (this.chartZoom) {
          this.chartZoom.dispose();
        }
        this.chartZoom = new ChartZoom(this.chartInstance);
      }
    },
    async getData(){
      try {
        const node = this.selectedNode;
        if (!node || !node.id) {
          console.log('没有选中节点或ID无效');
          return;
        }

        // console.log('正在获取趋势图数据，ID:', node.id);
        const result = await getTrend(node.id);
        // console.log('趋势图数据：', result.data);

        if (result.data && result.data.x && result.data.y) {
          this.xAxisData = result.data.x;
          this.data = result.data.y;
          this.zData = result.data.z;  // 存储z数组数据
          // console.log('数据更新成功，数据点数:', this.data.length);

          // 判断数组是否为空
          if (result.data.x.length > 0 && result.data.y.length > 0) {
            this.hasData = true;
            // 初始化默认坐标轴范围
            this.initDefaultAxisRange();
          } else {
            // 空数组情况
            this.hasData = false;
          }
        }else {
          // 明确设置数据为空
          this.hasData = false;
          this.xAxisData = [];
          this.data = [];
          this.zData = [];
        }
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },
    // 初始化默认范围
    initDefaultAxisRange() {
      if (this.xAxisData && this.xAxisData.length > 0 && this.data && this.data.length > 0) {
        // X轴默认范围
        this.defaultAxisRange.xMin = this.xAxisData[0];
        this.defaultAxisRange.xMax = this.xAxisData[this.xAxisData.length - 1];

        // Y轴默认范围
        const yValues = this.data;
        this.defaultAxisRange.yMin = 0; // 从0开始
        this.defaultAxisRange.yMax = Math.max(...yValues) * 1.2; // 最大值的1.2倍

        // 初始化当前范围
        this.axisRange = JSON.parse(JSON.stringify(this.defaultAxisRange));
        this.axisRange.timeRange = [this.defaultAxisRange.xMin, this.defaultAxisRange.xMax];

      }
    },

    // 重置范围
    resetAxisRange() {
      this.axisRange.timeRange = [this.defaultAxisRange.xMin, this.defaultAxisRange.xMax];
      this.axisRange.yMin = this.defaultAxisRange.yMin;
      this.axisRange.yMax = this.defaultAxisRange.yMax;
      this.applyAxisRange();
    },

    // 重置缩放
    resetZoom() {
      if (this.chartInstance) {
        // 使用图表缩放实例的resetZoom方法重置缩放
        if (this.chartZoom) {
          this.chartZoom.resetZoom();
        } else {
          // 如果图表缩放实例不存在，直接设置dataZoom
          this.chartInstance.setOption({
            dataZoom: [{
              start: 0,
              end: 100
            }]
          });
        }
      }
    },

    // 应用范围设置
    applyAxisRange() {
      if (!this.chartInstance || this.axisRange.timeRange.length !== 2) return;

      const [startTimeStr, endTimeStr] = this.axisRange.timeRange;

      const xMin = this.findNearestTime(startTimeStr);
      const xMax = this.findNearestTime(endTimeStr);

      this.chartInstance.setOption({
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0,
            startValue: xMin,
            endValue: xMax
          }
        ],
        yAxis: {
          min: this.axisRange.yMin,
          max: this.axisRange.yMax
        }
      });

      this.showAxisRangeDialog = false;
    },
    findNearestTime(targetTimeStr) {
      const target = new Date(targetTimeStr).getTime();
      let nearest = this.xAxisData[0];
      let minDiff = Math.abs(target - new Date(nearest).getTime());

      for (const item of this.xAxisData) {
        const diff = Math.abs(target - new Date(item).getTime());
        if (diff < minDiff) {
          minDiff = diff;
          nearest = item;
        }
      }

      return nearest;
    },
    // 更新图表配置
    updateChart() {
      if (!this.chart) return;

      try {
        // 如果没有数据，则只显示提示信息，隐藏所有其他元素
        if (!this.hasData) {
          this.chart.setOption({
            grid: { show: false },
            xAxis: { show: false },
            yAxis: { show: false },
            series: [],
            dataZoom: [],
            title: {
              show: true,
              text: '暂无趋势数据',
              textStyle: {
                color: '#999',
                fontSize: 14
              },
              left: 'center',
              top: 'center'
            }
          }, true); // 第二个参数设为 true 表示完全重置选项
          return;
        }
        // 获取数据中的最大 x 值
        const maxX = this.data.length > 0 ? this.data[this.data.length - 1][0] : 0;
        const self = this; // 保存 this 引用用于 tooltip formatter

        const option = {
          grid: { left: '2%', right: '3.5%', top: this.isExpanded ? '5%' : '14%', bottom: '3%', containLabel: true },
          tooltip: {
            show: false
          },
          toolbox: {
            feature: {
/*               dataZoom: {
                yAxisIndex: 'none',
                title: {
                  zoom: '区域缩放',
                  back: '还原缩放'
                }
              }, */
              /* restore: { title: '还原' }, */
/*               saveAsImage: { title: '保存' } */
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: true,
            max: maxX,
            name: ' ',
            data: this.xAxisData,
            min: this.axisRange.xMin,
            max: this.axisRange.xMax
          },
          yAxis: {
            type: 'value',
            name: 'm/s²',
            nameLocation: 'end', // 单位位置为中间
            nameTextStyle: {
              align: "left"
            },
            min: this.axisRange.yMin,
            max: this.axisRange.yMax,
            axisLabel: {
              formatter: function(value) {
                return value.toFixed(4);
              }
            }
          },
          dataZoom: [
            {
              type: 'inside',
              xAxisIndex: 0,
              start: 0,
              end: 100,
              minValueSpan: 10,
              filterMode: 'none'
            },
            {
              type: 'inside',
              yAxisIndex: 0,
              start: 0,
              end: 100,
              minValueSpan: 10,
              filterMode: 'none'
            }
          ],
          series: [
            {
              type: 'line',
              data: this.data,
              sampling: 'none',
              smooth: true,
              showSymbol: true,
              symbolSize: 6,
              symbol: 'circle',
              itemStyle: {
                color: '#4985DF',
                borderWidth: 2,
                borderColor: '#fff'
              },
              lineStyle: {
                color: '#4985DF',
                width: 2
              },
              emphasis: {
                scale: 1.5,
                focus: 'series',
                itemStyle: {
                  borderWidth: 3
                }
              }
            }
          ]
        }

        this.chart.setOption(option, true)

        // 重新绑定点击事件
        this.chart.off('click')
        this.chart.on('click', this.handleChartClick)

        // 添加缩放事件监听
        this.chart.off('datazoom')
        this.chart.on('datazoom', this.handleDataZoom)
      } catch (error) {
        console.error('更新图表时出错:', error)
      }
    },
    // 处理图表点击事件
    handleChartClick(params) {
      console.log('点击事件参数：', params);  // 保留这个日志
      if (!this.chart) return;

      let clickIndex;

      if (params.componentType === 'series') {
        // 直接点击在数据点上
        clickIndex = params.dataIndex;
      } else if (params.offsetX !== undefined && params.offsetY !== undefined) {
        // 点击在空白处，找到最近的点
        const xValue = this.chart.convertFromPixel({xAxisIndex: 0}, params.offsetX);
        const nearestPoint = this.findNearestPoint(xValue);
        if (!nearestPoint) return;
        console.log('nearestPoint', nearestPoint);
        clickIndex = nearestPoint.index;
      } else if(params.componentType === 'markLine' || params.componentType === 'markPoint') {
        this.clearMark();
        return;
      }

      if (clickIndex !== undefined) {
        this.selectPoint(clickIndex);
      }
    },
    // 选中数据点
    selectPoint(index) {
      if (index === undefined || index < 0 || index >= this.data.length) {
        return; // 无效索引
      }

      const xValue = this.xAxisData[index];
      const yValue = this.data[index];

      // 使用索引获取对应的z值作为device_id
      const device_id = this.zData[index];
      const { point_id } = this.$store.state.dataStore;
      const select_id = this.$store.state.tree.selectedTreeNode.id;

      if (!point_id) {
        this.$message.error('请先从数据表格中选择一个数据点');
        return;
      }
      if (!select_id) {
        this.$message.error('请先选择左侧树节点');
        return;
      }
      if (!device_id) {
        this.$message.error('无法获取设备ID');
        return;
      }

      // 更新选中点
      this.selectedPoint = { x: xValue, y: yValue };
      this.currentXValue = xValue;
      this.currentYValue = yValue;

      // 更新 Vuex 中的时间点状态，触发其他图表更新
      this.$store.dispatch('dataStore/setDeviceData', {
        device_id,
        point_id,
        time_point: xValue
      });

      // 更新标记
      this.updateMark();
    },
    // 移动到上一个数据点
    moveToPreviousPoint() {
      if (!this.selectedPoint) {
        this.$message.info('请先选择一个数据点');
        return;
      }
      const currentIndex = this.xAxisData.findIndex(x => x === this.selectedPoint.x);
      if (currentIndex > 0) {
        this.selectPoint(currentIndex - 1);
      } else {
        this.$message.info('已经是第一个点了');
      }
    },
    // 移动到下一个数据点
    moveToNextPoint() {
      if (!this.selectedPoint) {
        this.$message.info('请先选择一个数据点');
        return;
      }
      const currentIndex = this.xAxisData.findIndex(x => x === this.selectedPoint.x);
      if (currentIndex !== -1 && currentIndex < this.data.length - 1) {
        this.selectPoint(currentIndex + 1);
      } else {
        this.$message.info('已经是最后一个点了');
      }
    },
    // 更新标记
    updateMark() {
      if (!this.selectedPoint) return

      const { x, y } = this.selectedPoint
      const yAxisRange = this.chartInstance.getModel().getComponent('yAxis', 0).axis.scale.getExtent()

      // 设置标记线和标记点
      this.chartInstance.setOption({
        series: [{
          markLine: {
            silent: false,
            animation: false,
            symbol: ['none', 'none'],
            data: [[
              { xAxis: x, yAxis: yAxisRange[0] },
              {
                xAxis: x,
                yAxis: yAxisRange[1],
                lineStyle: {
                  color: '#FF0000',
                  width: 1,
                  type: 'dashed'
                }
              }
            ]]
          },
          markPoint: {
            animation: false,
            data: [{
              coord: [x, y],
              symbol: 'circle',
              symbolSize: 3,
              itemStyle: {
                color: '#FF0000'
              },
              label: {
                show: false
              }
            }]
          }
        }]
      })
    },
    // 清除标记
    clearMark() {
    // 清除所有标记
      this.chartInstance.setOption({
        series: [{
            markLine: { data: [] },
            markPoint: { data: [] }
          }]
      });
      this.selectedPoint = null;
      this.currentXValue = null;
      this.currentYValue = null;
    },
    // 处理数据缩放事件
    handleDataZoom() {
      if (!this.selectedPoint) return

      const { x, y } = this.selectedPoint
      const xPixel = this.chartInstance.convertToPixel({ xAxisIndex: 0 }, x)
      const yPixel = this.chartInstance.convertToPixel({ yAxisIndex: 0 }, y)
      const chartRect = this.$refs.chart.getBoundingClientRect()

      const isVisible = (
        xPixel >= 0 &&
        xPixel <= chartRect.width &&
        yPixel >= 0 &&
        yPixel <= chartRect.height
      )

      if (isVisible) {
        this.updateMark()
      } else {
        this.clearMark()
      }
    },
    // 找到最近的数据点
    findNearestPoint(xValue) {
      if (!this.xAxisData || !this.xAxisData.length) return null;

      // 找到最接近的数据点
      let nearestIndex = 0;
      let minDistance = Infinity;

      this.xAxisData.forEach((x, index) => {
        const distance = Math.abs(xValue - x);
        if (distance < minDistance) {
          minDistance = distance;
          nearestIndex = index;
        }
      });

      // 确保数据存在
      if (nearestIndex >= 0 && nearestIndex < this.data.length) {
        return {
          index: nearestIndex,
          xValue: this.xAxisData[nearestIndex],
          yValue: this.data[nearestIndex]
        };
      }

      return null;
    },
    // 添加处理键盘事件的方法
    handleKeyPress(e) {
      if (e.key === 'Enter' && this.chart) {
        // 获取当前选中点的数据
        const currentIndex = this.keyboardNavigation?.currentPointIndex;
        if (currentIndex !== undefined && currentIndex >= 0) {
          // 构造一个类似点击事件的参数对象
          const params = {
            componentType: 'series',
            dataIndex: currentIndex,
            name: this.xAxisData[currentIndex],
            value: this.data[currentIndex]
          };
          // 触发点击事件处理
          this.handleChartClick(params);
        }
      }
    },
    handleChartKeyDown(e) {
      // 处理Enter键，用于选中数据点
      if (e.key === 'Enter' && this.chart) {
        // 获取当前选中点的数据
        const currentIndex = this.keyboardNavigation?.currentPointIndex;
        if (currentIndex !== undefined && currentIndex >= 0) {
          // 构造一个类似点击事件的参数对象
          const params = {
            componentType: 'series',
            dataIndex: currentIndex,
            name: this.xAxisData[currentIndex],
            value: this.data[currentIndex]
          };
          // 触发点击事件处理
          this.handleChartClick(params);
        }
      }
      // 处理Delete键，用于删除数据点
      else if (e.key === 'Delete') {
        if (!this.selectedPoint || !this.zData) {
          this.$message.warning('请先选择一个数据点');
          return;
        }

        // 找到当前选中点的索引
        const selectedIndex = this.xAxisData.findIndex(x => x === this.selectedPoint.x);
        if (selectedIndex === -1) {
          this.$message.warning('无法找到选中的数据点');
          return;
        }

        // 获取对应的Z轴数据（device_id）
        const device_id = this.zData[selectedIndex];
        if (!device_id) {
          this.$message.warning('无法获取设备ID');
          return;
        }

        // 提示用户确认删除
        this.$confirm('确定要删除此测量记录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            // 调用删除接口
            await deleteTrend(device_id);
            // 先清除当前标记
            this.clearMark();
            // 等待数据重新加载完成
            await this.reloadChartData();
            // 成功消息显示
            this.$message.success('删除成功');
          } catch (error) {
            console.error('删除失败:', error);
            this.$message.error('删除失败');
          }
        }).catch(() => {
          // 用户取消删除
          this.$message.info('已取消删除');
        });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.qscontainer{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.trend-toolbar {
  padding: 0 10px;
  background: #f5f5f5;
  height: 30px;
  display: flex;
  align-items: center;
  gap: 10px;

  .fuction {
    display: flex;
    gap: 8px;

    .cursor-btn {
      min-width: 32px;
      height: 24px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: white;
      color: #595959;
      font-size: 13px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      padding: 0 8px;

      &:hover {
        background: #f0f0f0;
        border-color: #40a9ff;
        color: #40a9ff;
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }

  .display-data {
    display: flex;
    gap: 16px;
    align-items: center;
    font-size: 13px;
    color: #595959;

    div {
      white-space: nowrap;
    }
  }

  .delete-tip {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #ff4d4f;
    margin-left: 15px;

    i {
      margin-right: 5px;
      font-size: 14px;
    }
  }
}

.chart {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  box-sizing: border-box;
  background-color: #f5f5f5;
}

.top-container {
  width: 100%;
  height: 30px;
  padding: 3px;
  background-color: #f5f5f5;
  font-size: 14px;
  text-align: left;
  display: flex;
}

.btn{
  border: none;
}

.btn:active{
  transform:scale(0.9);
}

.axis-range-dialog {
  .axis-range-form {
    padding: 10px;

    .axis-group {
      margin-bottom: 20px;

      .axis-label {
        margin-bottom: 10px;
        font-weight: bold;
        color: #606266;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

:deep(.el-input-group__prepend) {
  padding: 0 10px;
}

:deep(.el-input__inner) {
  text-align: center;
}
.no-data-tip{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>

