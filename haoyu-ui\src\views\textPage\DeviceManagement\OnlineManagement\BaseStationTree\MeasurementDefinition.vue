<template>
  <div class="measurement-definition">
    <h2>测量定义</h2>

    <div class="section">
      <h3>波形采集间隔</h3>
      <div class="section-content">
        <el-form label-width="70px" label-position="left" class="compact-form">
          <div class="form-inline">
            <el-form-item label="波形：">
              <el-select v-model="form.waveform" size="mini" placeholder="请选择" @change="handleGapChange">
                <el-option
                  v-for="item in waveGapOptions"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>分钟
            </el-form-item>
            <el-form-item label="采样值：">
              <el-select v-model="form.samplingValue" size="mini" placeholder="请选择">
                <el-option
                  v-for="item in filteredGapOptions"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>分钟
            </el-form-item>
            <el-form-item label="采样时间">
              <el-select v-model="form.samplingTime" size="mini" placeholder="请选择">
                <el-option
                  v-for="item in collectTimeOptions"
                  :key="item"
                  :label="item"
                  :value="item">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>

<!--     <div class="section long-waveform">
      <h3>长波形</h3>
      <div class="long-waveform-content">
        <div class="button-group">
          <el-button type="default" size="small" @click="addRow">新增</el-button>
          <el-button type="default" size="small" @click="editRow">编辑</el-button>
          <el-button type="default" size="small" @click="deleteRow">删除</el-button>
          <el-button type="default" size="small">转速策略</el-button>
          <el-button type="default" size="small">应用到所有</el-button>
        </div>
        <div class="table-container">
          <el-table
            ref="longWaveformTable"
            :data="longWaveformData"
            width="100%"
            border
            stripe
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="signalType" label="信号类型" />
            <el-table-column prop="sensorType" label="传感器类型" />
            <el-table-column prop="waveformLength" label="波形长度" />
            <el-table-column prop="analysisFrequency" label="分析频率" />
            <el-table-column prop="collectionInterval" label="采集间隔" />
          </el-table>
        </div>
      </div>
    </div> -->

    <div class="section">
      <h3>高级设置</h3>
      <div class="section-content">
        <el-form label-width="130px" label-position="left" class="compact-form">
          <div class="form-inline">
            <el-form-item label="报警采集组数：">
              <el-input v-model="form.alarmCollectionGroup" size="mini" />
            </el-form-item>
            <el-form-item label="报警采集组数：">
              <el-input v-model="form.collectionGroup" size="mini" />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { getWaveformTimeInterval, getSamplingInterval, getWaveformInterval } from '../api/api'
import { mapGetters } from 'vuex'
export default {
  name: 'MeasurementDefinition',
  props: {
    mode: {
      type: String,
      default: 'add'
    },
    company: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        waveform: '',
        samplingValue: '',
        samplingTime: '',
        alarmCollectionGroup: '',
        collectionGroup: ''
      },
      // 定义下拉选项
      gapOptions: [], // 从API获取
      waveGapOptions: [], // 从API获取
      collectTimeOptions: [2, 4, 8],
      filteredGapOptions: [], // 根据波形值筛选的采样值选项
      longWaveformData: [
        { signalType: '类型A', sensorType: '传感器A', waveformLength: '100ms', analysisFrequency: '50Hz', collectionInterval: '10s' }
        // 更多数据...
      ],
      selectedRows: []
    }
  },
  computed: {
    ...mapGetters('SettingConnect', ['baseStationNode', 'selectedNodeID'])
  },
  watch: {
    // 监听选中节点ID的变化
    selectedNodeID: {
      handler(newVal) {
        if (newVal) {
          this.getWaveformTimeInterval();
        }
      },
      immediate: true
    },
    // 监听对话框可见性变化，当对话框打开时刷新数据
    visible: {
      handler(newVal) {
        if (newVal) {
          // 对话框打开时，确保选项数据已加载
          if (this.gapOptions.length === 0 || this.waveGapOptions.length === 0) {
            this.loadIntervalOptions();
          } else if (this.selectedNodeID) {
            // 如果选项数据已存在且有选中节点，直接获取设置值
            this.getWaveformTimeInterval();
          }
        }
      }
    }
  },
  mounted() {
    // 获取采样间隔和波形间隔的选项数据
    this.loadIntervalOptions()
  },
  methods: {
    // 加载间隔选项数据
    async loadIntervalOptions() {
      try {
        // 并行获取采样间隔和波形间隔数据
        const [samplingResponse, waveformResponse] = await Promise.all([
          getSamplingInterval(),
          getWaveformInterval()
        ]);

        // 处理采样间隔数据
        if (samplingResponse && samplingResponse.data) {
          this.gapOptions = samplingResponse.data.map(item => parseFloat(item.gap));
        }

        // 处理波形间隔数据
        if (waveformResponse && waveformResponse.data) {
          this.waveGapOptions = waveformResponse.data.map(item => parseFloat(item.waveGap));
        }

        console.log('采样间隔选项:', this.gapOptions);
        console.log('波形间隔选项:', this.waveGapOptions);

        // 如果当前有选中的节点ID，获取对应的设置值
        if (this.selectedNodeID) {
          this.getWaveformTimeInterval();
        }
      } catch (error) {
        console.error('获取间隔选项失败:', error);
        this.$message.error('获取间隔选项数据失败');
      }
    },

    // 重置表单数据的方法
    resetForm() {
      this.form = {
        waveform: '',
        samplingValue: '',
        samplingTime: '',
        alarmCollectionGroup: '',
        collectionGroup: ''
      };

      // 重置筛选后的选项
      this.filteredGapOptions = [...this.gapOptions];

      // 如果当前有选中的节点ID，重新获取数据
      if (this.selectedNodeID) {
        this.getWaveformTimeInterval();
      }
    },
    handleSelectionChange(val) {
      this.selectedRows = val
    },
    addRow() {
      this.longWaveformData.push({
        signalType: '',
        sensorType: '',
        waveformLength: '',
        analysisFrequency: '',
        collectionInterval: ''
      })
    },
    editRow() {
      if (this.selectedRows.length === 1) {
        // 在这里执行编辑逻辑
      } else {
        this.$message({
          type: 'warning',
          message: '请选中一行进行编辑'
        })
      }
    },
    deleteRow() {
      if (this.selectedRows.length > 0) {
        this.longWaveformData = this.longWaveformData.filter(
          row => !this.selectedRows.includes(row)
        )
        this.selectedRows = []
      } else {
        this.$message({
          type: 'warning',
          message: '请至少选中一行进行删除'
        })
      }
    },
    // 波形值变化时，更新采样值选项
    handleGapChange(value) {
      if (!value) {
        this.filteredGapOptions = [...this.gapOptions];
        return;
      }

      // 根据整数倍关系筛选采样值选项，采样值应该是波形值的因子（能整除波形值的数）
      // 使用浮点数安全的整除判断
      this.filteredGapOptions = this.gapOptions.filter(item => {
        const ratio = value / item;
        return Math.abs(ratio - Math.round(ratio)) < 0.0001; // 精度容差
      });

      // 如果当前选中的采样值不在筛选后的选项中，则重置
      if (!this.filteredGapOptions.includes(this.form.samplingValue)) {
        this.form.samplingValue = this.filteredGapOptions[0] || '';
      }
    },
    // 添加getWaveformTimeInterval方法实现
    async getWaveformTimeInterval() {
      try {
        // 获取当前选中的节点ID
        const id = this.selectedNodeID
        if (!id) {
          console.warn('没有选中的节点ID');
          return;
        }
        console.log('获取波形时间间隔数据，节点ID:', id);

        const response = await getWaveformTimeInterval(id)
        // 使用API返回的数据更新表单或其他相关状态
        if (response && response.data) {
          // 根据实际API返回结构处理数据
          console.log('获取波形时间间隔数据成功:', response.data);

          // 将API返回的字符串转换为数字进行比较
          const waveGap = parseFloat(response.data.waveGap);
          const gap = parseFloat(response.data.gap);
          const collectTime = parseInt(response.data.collectTime);

          // 设置波形间隔，确保选项存在
          this.form.waveform = this.waveGapOptions.includes(waveGap) ? waveGap : this.waveGapOptions[0];

          // 初始化筛选后的采样值选项
          this.handleGapChange(this.form.waveform);

          // 设置采样间隔，确保选项存在
          this.form.samplingValue = this.filteredGapOptions.includes(gap) ? gap : this.filteredGapOptions[0];

          // 设置采样时间，确保选项存在
          this.form.samplingTime = this.collectTimeOptions.includes(collectTime) ? collectTime : this.collectTimeOptions[0];
        }
      } catch (error) {
        console.error('获取波形时间间隔失败:', error)
        this.$message.error('获取波形时间间隔数据失败');
      }
    },
  }
}
</script>

<style scoped>
.measurement-definition {
  padding: 20px;
  overflow-y: auto;
  max-height: 100vh;
}

.section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  color: #333;
  border-bottom: 1px solid #dcdcdc;
  padding-bottom: 5px;
}

.section-content {
  padding: 10px;
  background-color: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.form-inline {
  display: flex;
  align-items: center;
}

.form-inline .el-form-item {
  margin-right: 10px;
}

.el-input__inner {
  font-size: 12px;
}

.el-form-item__label {
  font-size: 12px;
  padding-bottom: 5px;
}

.long-waveform-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 10px;
}

.button-group {
  display: flex;
  flex-direction: column;
  margin-right: 20px;
}

.button-group .el-button {
  margin: 5px 10px;
}

.table-container {
  flex-grow: 1;
  max-height: 200px;
  overflow-y: auto;
}

.el-table {
  width: 100%;
  flex-grow: 1;
}

.compact-form .el-form-item {
  margin-bottom: 10px;
}

</style>
