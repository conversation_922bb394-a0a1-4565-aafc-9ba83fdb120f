<template>
  <div class="chart-container">
    <component :is="chartComponent" ref="currentChartComponent" style="height: 100%; width: 100%;" />
    <!-- 遮罩层 -->
    <div v-if="showMask" class="company-mask">
      <img
        :src="maskImage"
        alt="mask"
        class="mask-image"
        @error="handleImageError"
        @load="handleImageLoad"
      />
      <div v-if="loading" class="loading-overlay">
        <i class="el-icon-loading"></i>
      </div>
    </div>
  </div>
</template>

<script>
import components from './ChartComponentsMap'
import { mapState} from 'vuex'
import { getCompanyImage, getGradingImage } from '@/api/system/company'

export default {
  data(){
    return {
      showMask: false,
      maskImage: '',
      defaultImage: 'https://via.placeholder.com/800x600?text=No+Image',
      loading: false
    }
  },
  props: {
    type: {
      type: String,
      required: true
    }
  },
  watch: {
    selectedTreeNode: {
      handler(newVal) {
        if (!newVal.id) return

        // 如果当前组件是AlarmView，不显示遮罩层
        if (this.chartComponent.name === 'AlarmView') {
          this.showMask = false;
          return;
        }

        // 根据treeIcon判断是否需要获取图片
        if (newVal.treeicon === 'company') {
          this.getNodeImage(newVal.id, 'company')
        } else if (newVal.treeicon === 'grading') {
          this.getNodeImage(newVal.id, 'grading')
        } else {
          this.showMask = false
          this.maskImage = ''
        }
      },
      deep: true
    }
  },
  computed: {
    ...mapState('tree', ['selectedTreeNode']),
    ...mapState('chartSwitcher', ['activeChart']),
    /* 采用动态加载和模块缓存去读取导入的模块，可以加快浏览器的加载速度，
    也就是在需要的时候再加载，已经加载过的模块可以重复加载 */
    chartComponent() {
      if (this.activeChart) {
        return components[this.activeChart] || components['TrendChart']
      }
      return components[this.type] || components['TrendChart']
    }
  },
  methods: {
    async getNodeImage(id, type) {
      this.loading = true
      try {
        const api = type === 'company' ? getCompanyImage : getGradingImage
        const response = await api(id)

        if (response.code === 200 && response.data && response.data.imgUrl) {
          this.maskImage = `http://${response.data.imgUrl}`
          this.showMask = true
        } else {
          this.handleImageError()
        }
      } catch (error) {
        console.error('获取图片失败:', error)
        this.handleImageError()
      } finally {
        this.loading = false
      }
    },
    handleImageError() {
      this.showMask = false
      this.maskImage = ''
      this.loading = false
    },
    handleImageLoad() {
      this.loading = false
    },
    // 计算图表大小随着分割线变化
    resizeChart() {
      if (this.$refs.currentChartComponent && this.$refs.currentChartComponent.resizeChart) {
        this.$refs.currentChartComponent.resizeChart()
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.company-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.mask-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
