
// 初始状态
const state = {
  device_id: null,
  point_id: null,
  time_point: null,
  outer_type: null,
  fs_lines: null,
  selectedRowsData:[],
  alarmDetailData: null
}

// mutations用于同步修改状态
const mutations = {
  SET_DEVICE_ID(state, id) {
    state.device_id = id
  },
  SET_POINT_ID(state, id) {
    state.point_id = id
  },
  SET_TIME_POINT(state, time) {
    state.time_point = time
  },
  SET_OUTER_TYPE(state, type) {
    state.outer_type = type
  },
  SET_FS_LINES(state, lines) {
    state.fs_lines = lines
  },
  SET_SELECTED_ROWS_DATA(state, data) {state.selectedRowsData = data},
  // 一次性设置多个字段
  SET_DEVICE_DATA(state, data) {
    if (data.device_id !== undefined) state.device_id = data.device_id
    if (data.point_id !== undefined) state.point_id = data.point_id
    if (data.time_point !== undefined) state.time_point = data.time_point
    if (data.outer_type !== undefined) state.outer_type = data.outer_type
    if (data.fs_lines !== undefined) state.fs_lines = data.fs_lines
  },
  SET_ALARM_DETAIL_DATA(state, data) {
    state.alarmDetailData = data
  }
}

// actions用于异步操作或复杂逻辑
const actions = {
  // 设置单个字段
  setDeviceId({ commit }, id) {
    commit('SET_DEVICE_ID', id)
  },
  setPointId({ commit }, id) {
    commit('SET_POINT_ID', id)
  },
  setTimePoint({ commit }, time) {
    commit('SET_TIME_POINT', time)
  },
  setOuterType({ commit }, type) {
    commit('SET_OUTER_TYPE', type)
  },
  setFsLines({ commit }, lines) {
    commit('SET_FS_LINES', lines)
  },
  setSelectedRowsData:({ commit }, data)=>{
    commit('SET_SELECTED_ROWS_DATA',data)
  },
  // 一次性设置多个字段
  setDeviceData({ commit }, data) {
    commit('SET_DEVICE_DATA', data)
  },
  SET_ALARM_DETAIL_DATA(state, data) {
    state.alarmDetailData = data
  }
}

// getters用于获取状态或计算派生值
const getters = {
  getDeviceId: state => state.device_id,
  getPointId: state => state.point_id,
  getTimePoint: state => state.time_point,
  getOuterType: state => state.outer_type,
  getFsLines: state => state.fs_lines,
  getSelectedRowsData: state => state.selectedRowsData,
  // 获取所有数据
  getAllDeviceData: state => ({
    device_id: state.device_id,
    point_id: state.point_id,
    time_point: state.time_point,
    outer_type: state.outer_type,
    fs_lines: state.fs_lines
  }),
  getAlarmDetailData: state => state.alarmDetailData
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
