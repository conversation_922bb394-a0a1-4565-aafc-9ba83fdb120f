<template>
  <div class="ai-chat-container">
    <!-- 悬浮图标 -->
    <div
      v-show="!isOpen"
      class="ai-chat-icon"
      v-draggable
      :class="{ 'is-dragging': isDragging }"
      :style="{
        left: position.x + 'px',
        top: position.y + 'px'
      }"
    >
      <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="ai-badge">
        <el-avatar :size="50" class="ai-avatar">
          <i class="el-icon-service"></i>
        </el-avatar>
      </el-badge>
    </div>

    <!-- 聊天窗口 -->
    <chat-window
      v-show="isOpen"
      ref="chatWindow"
      @close="toggleChat"
      :style="{ right: '20px', bottom: '20px' }"
    />
  </div>
</template>

<script>
import ChatWindow from './ChatWindow.vue'
import { Badge, Avatar } from 'element-ui'
import { chatService } from '@/api/aiChat'

const STORAGE_KEY = 'ai-chat-position'
const SNAP_THRESHOLD = 20 // 吸附阈值（像素）

export default {
  name: 'AiChat',
  components: {
    ChatWindow,
    ElBadge: Badge,
    ElAvatar: Avatar
  },
  directives: {
    draggable: {
      bind(el, binding, vnode) {
        const vm = vnode.context
        el.style.position = 'fixed'
        el.style.cursor = 'pointer'
        el.style.zIndex = 2000

        let pressTimer = null
        let isDragging = false
        let startX = 0
        let startY = 0
        let initialX = 0
        let initialY = 0

        const startDrag = function(e) {
          e.preventDefault()
          startX = e.clientX
          startY = e.clientY
          initialX = vm.position.x
          initialY = vm.position.y

          // 开始计时
          pressTimer = setTimeout(() => {
            el.style.cursor = 'move'
            isDragging = true
            vm.isDragging = true
          }, 500)

          // 监听移动和释放
          document.addEventListener('mousemove', onMouseMove)
          document.addEventListener('mouseup', onMouseUp)
        }

        const onMouseMove = function(e) {
          // 如果移动距离超过5px，开始判断是否应该拖动
          if (Math.abs(e.clientX - startX) > 5 || Math.abs(e.clientY - startY) > 5) {
            // 如果还在计时，说明还没到500ms，取消计时并标记为拖动
            if (pressTimer) {
              clearTimeout(pressTimer)
              pressTimer = null
              el.style.cursor = 'move'
              isDragging = true
              vm.isDragging = true
            }

            // 如果已经在拖动状态，更新位置
            if (isDragging) {
              const deltaX = e.clientX - startX
              const deltaY = e.clientY - startY
              let newX = initialX + deltaX
              let newY = initialY + deltaY

              // 限制在窗口范围内
              const windowWidth = window.innerWidth
              const windowHeight = window.innerHeight
              const elementWidth = el.offsetWidth
              const elementHeight = el.offsetHeight

              newX = Math.min(windowWidth - elementWidth, Math.max(0, newX))
              newY = Math.min(windowHeight - elementHeight, Math.max(0, newY))

              // 边缘吸附
              if (newX < SNAP_THRESHOLD) {
                newX = SNAP_THRESHOLD
              } else if (newX > windowWidth - elementWidth - SNAP_THRESHOLD) {
                newX = windowWidth - elementWidth - SNAP_THRESHOLD
              }

              vm.position = { x: newX, y: newY }
              localStorage.setItem(STORAGE_KEY, JSON.stringify(vm.position))
            }
          }
        }

        const onMouseUp = function(e) {
          // 清除所有监听器
          document.removeEventListener('mousemove', onMouseMove)
          document.removeEventListener('mouseup', onMouseUp)

          // 如果存在计时器，说明是短按，触发点击
          if (pressTimer) {
            clearTimeout(pressTimer)
            pressTimer = null
            // 如果移动距离小于5px，认为是点击
            if (Math.abs(e.clientX - startX) < 5 && Math.abs(e.clientY - startY) < 5) {
              vm.toggleChat()
            }
          }

          // 重置状态
          el.style.cursor = 'pointer'
          isDragging = false
          vm.isDragging = false
        }

        // 添加事件监听
        el.addEventListener('mousedown', startDrag)

        // 清理事件监听
        binding.def.unbind = function() {
          el.removeEventListener('mousedown', startDrag)
          document.removeEventListener('mousemove', onMouseMove)
          document.removeEventListener('mouseup', onMouseUp)
        }
      },
      unbind(el, binding) {
        if (binding.def.unbind) {
          binding.def.unbind()
        }
      }
    }
  },
  data() {
    // 从本地存储获取上次保存的位置，如果没有则默认放在右侧
    const savedPosition = JSON.parse(localStorage.getItem(STORAGE_KEY) || 'null')
    const defaultX = window.innerWidth - 70 // 50(头像) + 20(边距)
    return {
      isOpen: false,
      unreadCount: 0,
      isDragging: false,
      position: savedPosition || {
        x: defaultX,
        y: window.innerHeight / 2
      },
      messages: [],
      conversationId: null,
      errorMessage: ''
    }
  },
  methods: {
    toggleChat() {
      this.isOpen = !this.isOpen
      if (this.isOpen) {
        this.unreadCount = 0
      }
    },
    async createNewConversation() {
      console.log('createNewConversation called')  // 添加日志
      try {
        const response = await chatService.createConversation()
        console.log('创建对话成功:', response)  // 添加日志
        this.conversationId = response.conversation_id
        // 添加欢迎消息
        this.messages.push({
          content: '您好！我是AI客服助手，很高兴为您服务。请问有什么可以帮您的吗？',
          type: 'ai',
          timestamp: new Date().getTime()
        })
      } catch (error) {
        this.$message.error('连接AI助手失败，请稍后重试')
        console.error('创建对话失败:', error)
      }
    },
    async handleSend(message) {
      console.log('handleSend called with message:', message)  // 添加日志

      // 添加用户消息
      this.messages.push({
        content: message,
        type: 'user',
        timestamp: new Date().getTime()
      })

      try {
        console.log('准备发送消息到API, conversationId:', this.conversationId)  // 添加日志
        // 调用API获取回复
        const response = await chatService.sendMessage(message, this.conversationId)
        console.log('收到API响应:', response)  // 添加日志

        // 添加AI回复
        this.messages.push({
          content: response.answer,
          type: 'ai',
          timestamp: new Date().getTime()
        })

        if (!this.isOpen) {
          this.unreadCount++
        }
      } catch (error) {
        console.error('发送消息失败，完整错误:', error)  // 添加更详细的错误日志
        // 显示错误消息
        this.messages.push({
          content: '抱歉，我遇到了一些问题，请稍后再试。',
          type: 'ai',
          timestamp: new Date().getTime()
        })
      }
    },
    addMessage(message) {
      this.messages.push(message)
      if (message.type === 'ai' && !this.isOpen) {
        this.unreadCount++
      }
    }
  },
  mounted() {
    // 监听窗口大小变化，确保位置合法
    window.addEventListener('resize', () => {
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight
      const elementWidth = 50 // 头像大小
      const elementHeight = 50

      let { x, y } = this.position

      // 调整位置确保在视口内
      x = Math.min(windowWidth - elementWidth - SNAP_THRESHOLD, Math.max(SNAP_THRESHOLD, x))
      y = Math.min(windowHeight - elementHeight, Math.max(0, y))

      // 更新位置并保存
      this.position = { x, y }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.position))
    })
  }
}
</script>

<style scoped>
.ai-chat-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2000;
}

.ai-chat-icon {
  position: fixed;
  z-index: 2000;
  cursor: pointer;
  pointer-events: auto;
  transition: transform 0.2s;
  user-select: none;
}

.ai-chat-icon.is-dragging {
  transform: scale(1.1);
  opacity: 0.8;
}

.ai-chat-icon:hover::after {
  content: '长按可拖动';
  position: absolute;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
}

.ai-badge :deep(.el-badge__content) {
  background-color: #ff4d4f;
  border: none;
}

.ai-avatar {
  background-color: #409EFF;
  color: white;
  cursor: pointer;
}

.ai-avatar i {
  font-size: 28px;
  line-height: 50px;
}

.el-badge {
  display: block;
  cursor: pointer;
}
</style>

