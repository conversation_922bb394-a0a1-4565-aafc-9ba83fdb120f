<!-- 时域波形图 -->
<template>
  <div class="sycontainer">
    <div class="top-container">{{ treePathText }} 时域波形图 {{ time_point }}  {{ currentXValue }} s, {{ currentYValue }} m/s²
      <button class="btn" style="margin-left: auto; box-sizing: border-box; border-radius: 8px;
        width: 30px;height: 30px;" @click="handleToggle">
        <svg-icon icon-class='unfold' />
      </button>
    </div>
    <!-- 工具栏 -->
    <div class="spectrum-toolbar">
      <div class="fuction">
        <button
          class="cursor-btn"
          :class="{ 'active': showCursor1 }"
          @click="toggleCursor(1)"
        >
          A
        </button>
        <button
          class="cursor-btn"
          :class="{ 'active': showCursor2 }"
          @click="toggleCursor(2)"
        >
          B
        </button>
        <button
          class="cursor-btn"
          :class="{ 'active': showPeriodLinesA }"
          @click="togglePeriodLinesA"
          :disabled="!showCursor1 || !cursor1 || !cursor2"
        >
          A周期
        </button>
        <button
          class="cursor-btn"
          :class="{ 'active': showPeriodLinesB }"
          @click="togglePeriodLinesB"
          :disabled="!showCursor2 || !cursorB1 || !cursorB2"
        >
          B周期
        </button>
        <button
          class="cursor-btn"
          @click="downloadChart"
          title="下载图表"
        >
          下载
        </button>
        <button
          class="cursor-btn"
          @click="showAxisRangeDialog = true"
          title="设置坐标轴范围"
        >
          范围
        </button>

        <button
          class="cursor-btn"
          title="圆周波形图"
          @click="sideNavigationVisible = true"
        >
          圆周波形图
        </button>

        <div class="keyboard-help">
          <span class="help-icon"><i class="el-icon-question" style="color:#409EFF;cursor:pointer;"></i></span>
          <div class="help-content">
            <h4>游标A控制：</h4>
            <ul>
              <li>Ctrl + ← : 向左移动A1游标</li>
              <li>Ctrl + → : 向右移动A1游标</li>
              <li>Shift + ← : 向左移动A2游标</li>
              <li>Shift + → : 向右移动A2游标</li>
            </ul>
            <h4>游标B控制：</h4>
            <ul>
              <li>Ctrl + ← : 向左移动B1游标</li>
              <li>Ctrl + → : 向右移动B1游标</li>
              <li>Shift + ← : 向左移动B2游标</li>
              <li>Shift + → : 向右移动B2游标</li>
            </ul>
            <p class="note">注：游标B的快捷键在游标B显示时生效，游标A和游标B同时出现时优先移动游标B的游标</p>
          </div>
        </div>

      </div>
      <div class="display-data">
        <div>A1: {{ cursor1 ? cursor1.toFixed(4) : '-' }}</div>
        <div>A2: {{ cursor2 ? cursor2.toFixed(4) : '-' }}</div>
        <div>
          A2-A1 = {{ freqDiff ? freqDiff.toFixed(4) : '-' }}s
          <span v-if="linkedSourceFrequency">({{ linkedSourceFrequency.toFixed(4) }}Hz)</span>
          <span v-else-if="freqDiff && freqDiff !== 0">({{ (1 / freqDiff).toFixed(4) }}Hz)</span>
          <span v-else>(-Hz)</span>
        </div>
        <div>B1: {{ cursorB1 ? cursorB1.toFixed(4) : '-' }}</div>
        <div>B2: {{ cursorB2 ? cursorB2.toFixed(4) : '-' }}</div>
        <div>B2-B1 = {{ freqDiffB ? Number(freqDiffB).toFixed(4) : '-' }} ({{ freqDiffB ? (1/Number(freqDiffB)).toFixed(4) : '-' }}Hz)</div>
      </div>
    </div>
    <div ref="chart" class="chart"
         tabindex="0"
         v-on:keydown.left.exact="handleLeftArrow"
         v-on:keydown.right.exact="handleRightArrow"
         v-on:keydown.left.ctrl="handleCtrlLeftArrow"
         v-on:keydown.right.ctrl="handleCtrlRightArrow"
         v-on:keydown.left.shift="handleShiftLeftArrow"
         v-on:keydown.right.shift="handleShiftRightArrow"
         @mouseenter="handleChartMouseEnter"
         @mouseleave="handleChartMouseLeave">
      <!-- 无数据提示 -->
      <div v-if="!data || data.length === 0 || error" class="no-data-tip">暂无波形数据</div>
    </div>

    <!-- 添加坐标轴范围设置对话框 -->
    <el-dialog
      title="设置坐标轴范围"
      :visible.sync="showAxisRangeDialog"
      width="400px"
      :close-on-click-modal="false"
      :modal="false"
      custom-class="axis-range-dialog"
    >
      <div class="axis-range-form">
        <div class="axis-group">
          <div class="axis-label">X轴范围:</div>
          <el-input
            v-model="axisRange.xMin"
            placeholder="最小值"
            size="small"
            style="width: 45%; margin-right: 10px;"
            @keyup.enter.native="applyAxisRange"
          >
            <template slot="prepend">Min</template>
          </el-input>
          <el-input
            v-model="axisRange.xMax"
            placeholder="最大值"
            size="small"
            style="width: 45%;"
            @keyup.enter.native="applyAxisRange"
          >
            <template slot="prepend">Max</template>
          </el-input>
        </div>
        <div class="axis-group">
          <div class="axis-label">Y轴范围:</div>
          <el-input
            v-model="axisRange.yMin"
            placeholder="最小值"
            size="small"
            style="width: 45%; margin-right: 10px;"
            @keyup.enter.native="applyAxisRange"
          >
            <template slot="prepend">Min</template>
          </el-input>
          <el-input
            v-model="axisRange.yMax"
            placeholder="最大值"
            size="small"
            style="width: 45%;"
            @keyup.enter.native="applyAxisRange"
          >
            <template slot="prepend">Max</template>
          </el-input>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="resetAxisRange">重置</el-button>
        <el-button type="primary" @click="applyAxisRange">确定</el-button>
      </div>
    </el-dialog>

    <SideNavigation
      :visible.sync="sideNavigationVisible"
      :waveData="data"
      :freqDiff="Number(freqDiff)"
    />

  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mapState, mapActions } from 'vuex'
import { getWaveform } from './data'
import { getParams } from '@/utils/graph_Interface'
// import chartKeyboardNavigation from '../TimeDomainChart/chartKeyboardNavigation'
import html2canvas from 'html2canvas'
import { chartDownloadMixin } from '../mixins/chartDownload'
import { chartZoomMixin } from '../mixins/chartZoomMixin'
import SideNavigation from './components/SideNavigation.vue'
import dataManager from '@/utils/dataManager' // 导入数据管理器

export default {
  name: 'TimeDomainChart',
  mixins: [chartDownloadMixin, chartZoomMixin],
  props: {
    isExpanded: {
      type: Boolean,
      default: false
    }
  },
  components: {
    SideNavigation
  },

  data() {
    return {
      _isUpdatingFromVuexLink: false, // 新增标记
      linkedSourceFrequency: null, // 新增：用于存储联动时的源频率
      sideNavigationVisible: false,
      chartInstance: null,
      currentXValue: null,
      currentYValue: null,
      data: [], //当前显示数据
      originalData:[], //原始数据
      currentSelectedPoint: null,// 当前选中的点
      resizeObserver: null, // 添加 resizeObserver
      // keyboardNavigation: null, // 移除未使用的属性
      showCursor1: false,
      cursor1: null,
      cursor2: null,
      cursor1Index: null, // 存储A1游标的索引
      cursor2Index: null, // 存储A2游标的索引
      freqDiff: 0,
      ampDiff: 0,
      isDragging: false,
      activeCursor: null, // 当前正在拖动的游标
      cursor1Y: null,
      cursor2Y: null,
      isDraggingInterval: false, // 是否正在拖动整个区间
      intervalDragStartX: null, // 开始拖动时的位置
      intervalDragStartCursor1: null, // 开始拖动时的游标1位置
      intervalDragStartCursor2: null, // 开始拖动时的游标2位置
      showCursor2: false,  // 游标B的显示状态
      cursorB1: null,      // B1位置
      cursorB2: null,      // B2位置
      cursorB1Index: null, // 存储B1游标的索引
      cursorB2Index: null, // 存储B2游标的索引
      cursorB1Y: null,     // B1的Y值
      cursorB2Y: null,     // B2的Y值
      freqDiffB: 0,        // B1-B2差值
      ampDiffB: 0,         // B1-B2幅值差
      lastCursorPositions: {
        A: { cursor1: null, cursor2: null, cursor1Y: null, cursor2Y: null, cursor1Index: null, cursor2Index: null },
        B: { cursor1: null, cursor2: null, cursor1Y: null, cursor2Y: null, cursor1Index: null, cursor2Index: null }
      },
      showPeriodLinesA: false, // A游标的周期线显示状态
      showPeriodLinesB: false, // B游标的周期线显示状态
      periodLinesA: [], // A游标的周期线位置
      periodLinesB: [], // B游标的周期线位置
      showAxisRangeDialog: false,
      axisRange: {
        xMin: null,
        xMax: null,
        yMin: null,
        yMax: null
      },
      defaultAxisRange: {
        xMin: null,
        xMax: null,
        yMin: null,
        yMax: null
      },
      currentDataZoom: null, // 添加一个新的数据属性来存储 dataZoom 状态
      savedZoomState: null, // 新增：用于保存缩放状态
      dataFeatures: {
        ranges: {
          x: { min: null, max: null },
          y: { min: null, max: null }
        },
        density: {
          pointsPerUnit: null,
          averageSpacing: null
        },
        measureType: null
      },
      error: null, // 添加错误状态属性
    }
  },
  computed: {
    ...mapState('tree', ['treePathText']),
    ...mapState('dataStore', ['device_id', 'point_id', 'time_point']), // 映射 Vuex 状态
    ...mapState('SettingConnect', ['configNodeInfo']), // 添加 configNodeInfo 映射
    ...mapState('chartLink', ['baseFrequency']),
  },
  watch:{
    '$store.state.tree.selectedTreeNode.id':{
      async handler(newNodeId) {
        console.log('节点ID变化:', newNodeId);
        if (!newNodeId) return

        // 使用数据管理器检查趋势数据，避免重复请求
        let hasTrendData = false
        try {
          hasTrendData = await dataManager.hasTrendData(newNodeId)
        } catch (err) {
          console.error('检查趋势图数据失败:', err)
          hasTrendData = false
        }

        // 只有当有趋势图数据并且有表格数据时才获取波形数据
        if (hasTrendData && this.$store.state.dataStore && this.$store.state.dataStore.device_id) {
          if (this.chartInstance) {
            await this.getWaveformData();
            this.updateChart();
          } else {
            this.initChart();
            await this.getWaveformData();
          }
        } else {
          // 没有数据时，初始化图表并显示错误信息
          this.error = '暂无波形数据';
          if (!this.chartInstance) {
            this.initChart();
          }
          this.updateChart();
        }
      }
    },
    'time_point':{
      async handler(newTimePoint) {
        if (!newTimePoint) return

        const { device_id, point_id } = this.$store.state.dataStore;
        if (!device_id || !point_id) return; // 确保 device_id 和 point_id 有效

        // 使用数据管理器检查趋势数据，避免重复请求
        let hasTrendData = false
        try {
          const nodeId = this.$store.state.tree.selectedTreeNode?.id
          if (nodeId) {
            // 使用数据管理器检查趋势数据，避免重复请求
            hasTrendData = await dataManager.hasTrendData(nodeId)
          }
        } catch (err) {
          console.error('检查趋势图数据失败:', err)
          hasTrendData = false
        }

        if (hasTrendData) {
          await this.getWaveformData()
          this.initChart() // 再初始化图表
          this.updateChart()
        } else {
          // 没有趋势图数据，显示错误信息
          this.error = '暂无波形数据'
          if (!this.chartInstance) {
            this.initChart()
          }
          this.updateChart()
        }
      }
    },
    isExpanded: {
      handler(newVal) {
        // 先让 DOM 完成更新
        this.$nextTick(() => {
          // 给足够的时间让过渡动画完成
          setTimeout(() => {
            if (this.chartInstance) {
              this.chartInstance.resize();
              // 使用 requestAnimationFrame 确保在下一帧更新
              requestAnimationFrame(() => {
                this.updateChart();
              });
            }
          }, 300)
        })
      },
      immediate: true
    },
    'configNodeInfo': {
      async handler(newVal, oldVal) {
        // 重置游标状态
        this.showCursor1 = false;
        this.cursor1 = null;
        this.cursor2 = null;
        this.cursor1Y = null;
        this.cursor2Y = null;
        this.freqDiff = 0;
        this.ampDiff = 0;

        // 重置缩放状态
        this.currentDataZoom = null;

        // 重置坐标轴范围
        this.axisRange = {
          xMin: null,
          xMax: null,
          yMin: null,
          yMax: null
        };

        try {
          // 在下一个 tick 重新获取数据并更新图表
          await this.$nextTick();
          if (this.chartInstance) {
            // 获取新数据
            await this.getWaveformData();

            // 重新初始化默认范围
            this.initDefaultAxisRange();

            // 分析新数据特征
            this.analyzeDataFeatures();

            // 重新初始化图表
            this.initChart();

            // 更新图表
            this.updateChart();
          }
        } catch (error) {
          console.error('切换测量定义时发生错误:', error);
        }
      },
      deep: true
    },
    cursor1() {
      if (this.showPeriodLinesA) {
        requestAnimationFrame(() => {
          this.updateChart();
        });
      }
    },
    cursor2() {
      if (this.showPeriodLinesA) {
        requestAnimationFrame(() => {
          this.updateChart();
        });
      }
    },
    'chartInstance.getOption().dataZoom'() {
      if (this.showPeriodLinesA) {
        this.calculatePeriodLinesA();
        this.updateChart();
      }
    },
    showPeriodLinesA() {
      this.$nextTick(() => {
        this.updateChart();
      });
    },
    showPeriodLinesB() {
      this.$nextTick(() => {
        this.updateChart();
      });
    },
    freqDiff(newDiff, oldDiff) {
    // 如果本次更新是来自 Vuex 的联动，则直接忽略，不反向发送更新
    if (this._isUpdatingFromVuexLink) {
      return;
    }

    // 只有当差值有效，且不是初始加载时，才发送更新
    if (newDiff > 0 && newDiff !== oldDiff) {
      const frequencyInHz = 1 / newDiff;

      // 在 dispatch 前，设置标志位，表明“我正要发起更新”
      this._isUpdatingFromVuexLink = true;
      this.$store.dispatch('chartLink/setBaseFrequency', frequencyInHz);

      // 异步重置标志位，让下一次来自外部的更新可以被接收
      this.$nextTick(() => {
        this._isUpdatingFromVuexLink = false;
      });
    }
    },

    // 2. 监听来自频谱图的频率联动
    baseFrequency: {
      handler(newFreq, oldFreq) {
        // 关键：如果标志位为 true，说明这次 baseFrequency 的变化是本组件自己刚刚触发的，
        // 必须忽略它，否则就会导致游标被“拉”回去。
        if (this._isUpdatingFromVuexLink) {
          return;
        }

        // --- 下面的逻辑只在真正接收到外部（频谱图）更新时执行 ---

        // 设置标志位，表示后续的游标和 freqDiff 变化都源于这次外部联动
        this._isUpdatingFromVuexLink = true;

        if (newFreq === null || newFreq === undefined || newFreq <= 0) {
          this.linkedSourceFrequency = null;
        } else if (this.data && this.data.length > 0) {
          this.linkedSourceFrequency = newFreq; // 保存源频率用于显示

          const periodT = 1 / newFreq;

          // 尝试将游标设置在图表可见范围的中心区域，以提高可见性
          const xAxis = this.chartInstance.getModel().getComponent('xAxis', 0).axis;
          const [startX, endX] = xAxis.scale.getExtent();
          const viewCenter = (startX + endX) / 2;

          const nearestPoint1 = this.findNearestDataPoint(viewCenter);
          const nearestPoint2 = this.findNearestDataPoint(nearestPoint1[0] + periodT);

          if (nearestPoint1 && nearestPoint2) {
            this.cursor1 = nearestPoint1[0];
            this.cursor2 = nearestPoint2[0];
            this.cursor1Index = this.findIndexOfPoint(nearestPoint1);
            this.cursor2Index = this.findIndexOfPoint(nearestPoint2);
          }

          if (!this.showCursor1) {
            this.showCursor1 = true;
          }
        }

        this.calculateDiff();
        this.updateChart();

        // 在所有更新完成后，异步地重置标志位，准备接收下一次外部更新
        this.$nextTick(() => {
            this._isUpdatingFromVuexLink = false;
        });
      },
    },
    data: {
      handler(newData) {
        if (newData && newData.length > 0) {
          this.initDefaultAxisRange();
        }
      },
      immediate: true
    },
    chartInstance: {
      handler(newInstance) {
        if (newInstance) {
          // 监听图表的 datazoom 事件
          newInstance.on('datazoom', () => {
            this.currentDataZoom = newInstance.getOption().dataZoom
            this.handleDataZoomChange()
          })
        }
      },
      immediate: true
    }
  },
  async mounted() {
    try {
      await this.checkDataStore()
      // 初始化图表实例
      if (this.$refs.chart) {
        this.initChart()
      }

      if (this.$store.state.dataStore && this.$store.state.dataStore.device_id) {
        await this.getWaveformData()
      } else {
        // 如果没有数据，设置错误状态
        this.error = '暂无波形数据'
        this.updateChart()
      }

      // 创建 ResizeObserver 实例
      this.resizeObserver = new ResizeObserver(() => {
        if (this.chartInstance) {
          this.chartInstance.resize()
        }
      })

      // 确保 chart ref 存在
      if (this.$refs.chart) {
        // 监听图表容器
        this.resizeObserver.observe(this.$refs.chart)
      }
    } catch (error) {
      console.error('Chart initialization failed:', error)
      this.error = '暂无波形数据'
      if (this.chartInstance) {
        this.updateChart()
      }
    }
  },
  beforeDestroy() {
    if (this.chartInstance) {
      const zr = this.chartInstance.getZr();
      zr.off('mousedown', this.handleMouseDown);
      zr.off('mousemove', this.handleMouseMove);
      zr.off('mouseup', this.handleMouseUp);
      zr.off('globalout', this.handleMouseUp);
      zr.off('dataZoom', this.handleDataZoom);
      this.chartInstance.off('datazoom', this.handleDataZoomChange);
      this.chartInstance.dispose();
    }
    // 清理游标状态
    this.cursorB = null;
    this.isCursorBVisible = false;
    this.cursorBPosition = null;

    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    // 移除周期线相关的事件监听
    if (this.chartInstance) {
      this.chartInstance.getZr().off('mousedown', this.handlePeriodLineMouseDown);
    }
  },
  methods: {
    ...mapActions('tree', ['setTreePathText']),

    handleToggle() {
      this.$emit('toggleExpand')
    },
    initChart() {
      if (this.$refs.chart) {
        if (this.chartInstance) {
          this.chartInstance.dispose();
        }

        this.chartInstance = echarts.init(this.$refs.chart)
        // 设置 chart 属性供 mixin 使用
        this.chart = this.chartInstance

        // 初始化缩放事件
        this.initZoomEvents(this.data.length)

        // 添加事件监听
        const zr = this.chartInstance.getZr()
        zr.on('mousedown', this.handleMouseDown)
        zr.on('mousemove', this.handleMouseMove)
        zr.on('mouseup', this.handleMouseUp)
        zr.on('globalout', this.handleMouseUp)

        // 监听缩放事件
        this.chartInstance.on('datazoom', (params) => {
          this.currentDataZoom = this.chartInstance.getOption().dataZoom;
          this.handleDataZoomChange();
        });

        // 添加点击事件
        this.chartInstance.on('click', this.handleChartClick);
      }
    },
    updateChart() {
      if (!this.chartInstance) return;

      try {
        // 如果有错误或没有数据，则显示无数据提示
        if (this.error || !this.data || this.data.length === 0) {
          this.chartInstance.clear();  // 清空之前的图表
          this.chartInstance.setOption({
            title: {
              show: true,
              text: '暂无波形数据',
              textStyle: {
                color: '#999',
                fontSize: 14
              },
              left: 'center',
              top: 'center'
            },
            grid: { show: false },
            xAxis: { show: false },
            yAxis: { show: false },
            series: []
          }, true);
          // 即使没有数据也处理标记重置
          if (this._isUpdatingFromVuexLink) {
            setTimeout(() => {
              this._isUpdatingFromVuexLink = false;
            }, 0);
          }
          return;
        }

        // 获取Y轴范围，如果获取失败则使用默认值
        const yAxisExtent = this.getYAxisExtent();

        const option = {
          animation: false,
          grid: {
            left: '0%',
            right: '3.5%',
            top: this.isExpanded ? '5%' : '14%',
            bottom: '0%',
            containLabel: true
          },
          dataZoom: this.generateZoomConfig(),
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              },
              lineStyle: {
                color: '#6a7985',
                width: 1,
                type: 'dashed'
              }
            },
            backgroundColor: 'rgba(50,50,50,0.7)',
            borderColor: '#333',
            borderWidth: 0,
            padding: [5,10],
            textStyle: {
              color: '#fff'
            },
            formatter: (params) => {
              const data = params[0];
              if (!data) return '';
              this.currentXValue = data.value[0];
              this.currentYValue = data.value[1];
              if (this.keyboardNavigation) {
                this.keyboardNavigation.currentPointIndex = data.dataIndex;
              }
              return `时间: ${this.currentXValue} s<br/>幅值: ${this.currentYValue} m/s²`;
            }
          },
          xAxis: {
            type: 'value',
            name: 's',
            min: this.axisRange.xMin.toFixed(4) !== null ? Number(this.axisRange.xMin.toFixed(4)) : undefined,
            max: this.axisRange.xMax.toFixed(4) !== null ? Number(this.axisRange.xMax.toFixed(4)) : undefined,
            axisLabel: {
              formatter: (value) => value.toFixed(4)
            }
          },
          yAxis: {
            type: 'value',
            name: 'm/s²',
            min: this.axisRange.yMin.toFixed(4),
            max: this.axisRange.yMax.toFixed(4),
            axisLabel: {
              formatter: (value) => value.toFixed(4)
            }
          },
          series: [{
            type: 'line',
            Smooth: true,
            animation: true,
            sampling: 'none',
            symbolSize: 3,
            showSymbol: false,
            data: this.data,
            itemStyle: {
              color: '#4985DF'
            },
            lineStyle: {
              color: '#4985DF'
            },
            emphasis: {
              scale: true,
              focus: 'series'
            },
            markLine: {
              silent: true,
              symbol: ['arrow', 'circle'],
              animation: false,
              data: [
                // A1游标
                ...(this.showCursor1 && this.cursor1 !== null ? [[
                  { coord: [this.cursor1, yAxisExtent[0]],
                    lineStyle: {
                      color: 'red',
                      width: 1,
                      type: 'solid'
                    }
                  },
                  {
                    coord: [this.cursor1, yAxisExtent[1]],
                    lineStyle: {
                      color: 'red',
                      width: 1,
                      type: 'solid'
                    },
                    label: {
                      formatter: `A1: ${this.cursor1.toFixed(4)}s`,
                      position: 'end'
                    }
                  }
                ]] : []),
                // A2游标
                ...(this.showCursor1 && this.cursor2 !== null ? [[
                  { coord: [this.cursor2, yAxisExtent[0]],
                    lineStyle: {
                      color: 'red',
                      width: 1,
                      type: 'solid'
                    }
                  },
                  {
                    coord: [this.cursor2, yAxisExtent[1]],
                    lineStyle: {
                      color: 'red',
                      width: 1,
                      type: 'solid'
                    },
                    label: {
                      formatter: `A2: ${this.cursor2.toFixed(4)}s`,
                      position: 'end'
                    }
                  }
                ]] : []),
                // B1游标
                ...(this.showCursor2 && this.cursorB1 !== null ? [[
                  { coord: [this.cursorB1, yAxisExtent[0]],
                    lineStyle: {
                      color: '#52c41a',
                      width: 1,
                      type: 'solid'
                    }
                  },
                  {
                    coord: [this.cursorB1, yAxisExtent[1]],
                    lineStyle: {
                      color: '#52c41a',
                      width: 1,
                      type: 'solid'
                    },
                    label: {
                      formatter: `B1: ${this.cursorB1.toFixed(4)}s`,
                      position: 'end'
                    }
                  }
                ]] : []),
                // B2游标
                ...(this.showCursor2 && this.cursorB2 !== null ? [[
                  { coord: [this.cursorB2, yAxisExtent[0]],
                    lineStyle: {
                      color: '#52c41a',
                      width: 1,
                      type: 'solid'
                    }
                  },
                  {
                    coord: [this.cursorB2, yAxisExtent[1]],
                    lineStyle: {
                      color: '#52c41a',
                      width: 1,
                      type: 'solid'
                    },
                    label: {
                      formatter: `B2: ${this.cursorB2.toFixed(4)}s`,
                      position: 'end'
                    }
                  }
                ]] : []),
                // A游标周期线
                ...(this.showPeriodLinesA ? this.calculatePeriodLinesA() : []),
                // B游标周期线
                ...(this.showPeriodLinesB ? this.calculatePeriodLinesB() : [])
              ]
            }
          }],
          // 添加连接线和拖动点
          graphic: [
            // 游标A的连接线和拖动点
            ...(this.showCursor1 && this.cursor1 !== null && this.cursor2 !== null ? [
              {
                type: 'line',
                id: 'cursorALine',
                shape: {
                  x1: this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursor1),
                  y1: this.chartInstance.getHeight() * 0.75,
                  x2: this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursor2),
                  y2: this.chartInstance.getHeight() * 0.75
                },
                style: {
                  stroke: '#ff4d4f',
                  lineWidth: 2
                },
                silent: true,
                z: 100
              },
              {
                type: 'circle',
                id: 'cursorADragPoint',
                shape: {
                  r: 5
                },
                position: [
                  this.chartInstance.convertToPixel({xAxisIndex: 0}, (this.cursor1 + this.cursor2) / 2),
                  this.chartInstance.getHeight() * 0.75
                ],
                style: {
                  fill: '#ff4d4f',
                  stroke: '#fff',
                  lineWidth: 2,
                  cursor: 'ew-resize'
                },
                draggable: 'horizontal',
                z: 101,
                ondragstart: () => {
                  this.isDraggingInterval = true;
                  this.intervalDragStartCursor1 = this.cursor1;
                  this.intervalDragStartCursor2 = this.cursor2;
                  this.intervalDragStartX = this.chartInstance.convertToPixel({xAxisIndex: 0}, (this.cursor1 + this.cursor2) / 2);
                },
                ondrag: (e) => {
                  if (!this.isDraggingInterval) return;
                  this.linkedSourceFrequency = null; // 清除联动频率标记

                  // 计算拖动的距离（在数据坐标系中）
                  const currentX = this.chartInstance.convertFromPixel({xAxisIndex: 0}, e.offsetX);
                  const deltaX = currentX - this.chartInstance.convertFromPixel({xAxisIndex: 0}, this.intervalDragStartX);

                  // 保持游标间距不变，同时移动两个游标
                  let newCursor1 = this.intervalDragStartCursor1 + deltaX;
                  let newCursor2 = this.intervalDragStartCursor2 + deltaX;

                  // 确保不超出数据范围
                  const maxX = this.data[this.data.length - 1][0];
                  if (newCursor1 >= 0 && newCursor2 <= maxX) {
                    // 找到最接近的数据点
                    const nearestPoint1 = this.findNearestDataPoint(newCursor1);
                    const nearestPoint2 = this.findNearestDataPoint(newCursor2);

                    if (nearestPoint1 && nearestPoint2) {
                      // 直接更新游标位置
                      this.cursor1 = nearestPoint1[0];
                      this.cursor2 = nearestPoint2[0];
                      this.cursor1Y = nearestPoint1[1];
                      this.cursor2Y = nearestPoint2[1];

                      // 立即更新连接线和拖动点的位置
                      const x1 = this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursor1);
                      const x2 = this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursor2);
                      const y = this.chartInstance.getHeight() * 0.75;

                      // 更新连接线
                      e.target.parent.childAt(0).attr({
                        shape: {
                          x1: x1,
                          y1: y,
                          x2: x2,
                          y2: y
                        }
                      });

                      // 更新拖动点位置
                      e.target.attr({
                        position: [
                          (x1 + x2) / 2,
                          y
                        ]
                      });

                      this.calculateDiff();

                      // 使用 requestAnimationFrame 更新游标线
                      requestAnimationFrame(() => {
                        this.updateChart();
                      });
                    }
                  }
                },
                ondragend: () => {
                  this.isDraggingInterval = false;
                  this.intervalDragStartX = null;
                  this.intervalDragStartCursor1 = null;
                  this.intervalDragStartCursor2 = null;
                }
              }
            ] : []),
            // 游标B的连接线和拖动点
            ...(this.showCursor2 && this.cursorB1 !== null && this.cursorB2 !== null ? [
              {
                type: 'line',
                id: 'cursorBLine',
                shape: {
                  x1: this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursorB1),
                  y1: this.chartInstance.getHeight() * 0.65,
                  x2: this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursorB2),
                  y2: this.chartInstance.getHeight() * 0.65
                },
                style: {
                  stroke: '#52c41a',
                  lineWidth: 2
                },
                silent: true,
                z: 100
              },
              {
                type: 'circle',
                id: 'cursorBDragPoint',
                shape: {
                  r: 5
                },
                position: [
                  this.chartInstance.convertToPixel({xAxisIndex: 0}, (this.cursorB1 + this.cursorB2) / 2),
                  this.chartInstance.getHeight() * 0.65
                ],
                style: {
                  fill: '#52c41a',
                  stroke: '#fff',
                  lineWidth: 2,
                  cursor: 'ew-resize'
                },
                draggable: 'horizontal',
                z: 101,
                ondragstart: () => {
                  this.isDraggingIntervalB = true;
                  this.intervalDragStartCursorB1 = this.cursorB1;
                  this.intervalDragStartCursorB2 = this.cursorB2;
                  this.intervalDragStartX = this.chartInstance.convertToPixel({xAxisIndex: 0}, (this.cursorB1 + this.cursorB2) / 2);
                },
                ondrag: (e) => {
                  if (!this.isDraggingIntervalB) return;
                  this.linkedSourceFrequency = null; // 清除联动频率标记

                  // 计算拖动的距离（在数据坐标系中）
                  const currentX = this.chartInstance.convertFromPixel({xAxisIndex: 0}, e.offsetX);
                  const deltaX = currentX - this.chartInstance.convertFromPixel({xAxisIndex: 0}, this.intervalDragStartX);

                  // 保持游标间距不变，同时移动两个游标
                  let newCursorB1 = this.intervalDragStartCursorB1 + deltaX;
                  let newCursorB2 = this.intervalDragStartCursorB2 + deltaX;

                  // 确保不超出数据范围
                  const maxX = this.data[this.data.length - 1][0];
                  if (newCursorB1 >= 0 && newCursorB2 <= maxX) {
                    // 找到最接近的数据点
                    const nearestPoint1 = this.findNearestDataPoint(newCursorB1);
                    const nearestPoint2 = this.findNearestDataPoint(newCursorB2);

                    if (nearestPoint1 && nearestPoint2) {
                      // 直接更新游标位置
                      this.cursorB1 = nearestPoint1[0];
                      this.cursorB2 = nearestPoint2[0];
                      this.cursorB1Y = nearestPoint1[1];
                      this.cursorB2Y = nearestPoint2[1];

                      // 立即更新连接线和拖动点的位置
                      const x1 = this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursorB1);
                      const x2 = this.chartInstance.convertToPixel({xAxisIndex: 0}, this.cursorB2);
                      const y = this.chartInstance.getHeight() * 0.65;

                      // 更新连接线
                      e.target.parent.childAt(0).attr({
                        shape: {
                          x1: x1,
                          y1: y,
                          x2: x2,
                          y2: y
                        }
                      });

                      // 更新拖动点位置
                      e.target.attr({
                        position: [
                          (x1 + x2) / 2,
                          y
                        ]
                      });

                      this.calculateDiff();

                      // 使用 requestAnimationFrame 更新游标线
                      requestAnimationFrame(() => {
                        this.updateChart();
                      });
                    }
                  }
                },
                ondragend: () => {
                  this.isDraggingIntervalB = false;
                  this.intervalDragStartX = null;
                  this.intervalDragStartCursorB1 = null;
                  this.intervalDragStartCursorB2 = null;
                }
              }
            ] : [])
          ]
        };

        this.chartInstance.setOption(option, {
          lazyUpdate: false,
          silent: true
        });

        // 根据当前的标记状态确定是否应尝试获取焦点
        const attemptFocus = (this.showCursor1 || this.showCursor2) && !this._isUpdatingFromVuexLink;

        // 如果此更新是 Vuex 联动的一部分，则安排标记重置
        if (this._isUpdatingFromVuexLink) {
          setTimeout(() => {
            this._isUpdatingFromVuexLink = false;
          }, 0);
        }

        // 如果在考虑标记重置之前条件满足，则尝试获取焦点
        if (attemptFocus) {
          this.$nextTick(() => {
            if (this.$refs.chart) {
              this.$refs.chart.focus();
            }
          });
        }
      } catch (error) {
        console.warn('更新图表时发生错误:', error);
        // 发生错误时也尝试重置标记
        if (this._isUpdatingFromVuexLink) {
          setTimeout(() => {
            this._isUpdatingFromVuexLink = false;
          }, 0);
        }
      }
    },
    getYAxisExtent() {
      try {
        if (!this.chartInstance) return [0, 0];
        const yAxis = this.chartInstance.getModel()?.getComponent('yAxis', 0);
        if (!yAxis || !yAxis.axis || !yAxis.axis.scale) return [0, 0];
        return yAxis.axis.scale.getExtent();
      } catch (error) {
        console.warn('获取Y轴范围时发生错误:', error);
        return [0, 0];
      }
    },
    // 处理图表点击事件
    handleChartClick(params) {
      if (!this.isHoverMode && params.componentType === 'series') {
        const xValue = params.value[0]
        const yValue = params.value[1]
        this.currentXValue = xValue
        this.currentYValue = yValue
        // 更新当前中点
        this.currentSelectedPoint = { x: this.currentXValue, y: this.currentYValue }
        //打印一下试试
        console.log(this.currentSelectedPoint);
      }else if(params.componentType === 'markPoint') {
        this.clearMark();
        return;
      }else if(params.componentType === 'markLine') {
        return;
      }
      // 更新标记
      this.updateMark()
    },
    // 更新标记
    updateMark(){
      if (!this.currentSelectedPoint) return

      const { x, y } = this.currentSelectedPoint
      const yAxisRange = this.chartInstance.getModel().getComponent('yAxis', 0).axis.scale.getExtent()
      this.chartInstance.setOption({
          series: [{
            markLine: {
              silent: false,
              animation: false,
              symbol: ['none', 'none'],
              data: [[
                { xAxis: x, yAxis: yAxisRange[0] },
                {
                  xAxis: x,
                  yAxis: yAxisRange[1],
                  lineStyle: {
                    color: '#FF0000',
                    width: 1,
                    type: 'dashed'
                  }
                }
              ]]
            },
            markPoint: {
              animation: false,
              data: [{
                coord: [x, y],
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                  color: '#FF0000'
                },
                label: {
                  show: true,
                  formatter: `频率: ${x} s\n幅度: ${y} m/s²`,
                  position: 'right',
                  backgroundColor: 'rgba(50, 50, 50, 0.9)',
                  padding: [5, 10],
                  borderRadius: 4,
                  color: '#fff'
                }
              }]
            }
          }]
      })
    },
    // 清除标记
    clearMark() {
      this.chartInstance.setOption({
        series: [{
          markLine: {
            data: []
          },
          markPoint: {
            data: []
          }
        }]
      })
      this.currentSelectedPoint = null
    },
    // 处理数据缩放事件
    handleDataZoom(params) {
      // 保存当前的游标位置
      const cursors = {
        cursor1: this.cursor1,
        cursor2: this.cursor2,
        cursorB1: this.cursorB1,
        cursorB2: this.cursorB2
      };

      // 在下一帧更新图表
      requestAnimationFrame(() => {
        // 恢复游标位置
        this.cursor1 = cursors.cursor1;
        this.cursor2 = cursors.cursor2;
        this.cursorB1 = cursors.cursorB1;
        this.cursorB2 = cursors.cursorB2;

        // 更新图表
        if (this.showCursor1 || this.showCursor2) {
          this.updateChart();
        }
      });
    },

    async getWaveformData() {
      try {
        const dataStore = this.$store.state.dataStore
        if (!dataStore.device_id || !dataStore.point_id || !dataStore.time_point) {
          this.error = '暂无波形数据'
          this.data = []
          this.originalData = []
          return
        }

        // 获取参数前重置数据
        this.data = []
        this.originalData = []

        // 检查表格是否有数据 - 通过检查dataStore的device_id, point_id, time_point是否存在来判断
        const hasTableData = dataStore.device_id && dataStore.point_id && dataStore.time_point

        // 检查当前节点的趋势图是否有数据
        let hasTrendData = false
        try {
          // 获取当前节点ID
          const nodeId = this.$store.state.tree.selectedTreeNode?.id
          if (nodeId) {
            // 使用数据管理器检查趋势数据，避免重复请求
            hasTrendData = await dataManager.hasTrendData(nodeId)
          }
        } catch (err) {
          console.error('检查趋势图数据失败:', err)
          hasTrendData = false
        }

        // 如果表格没有数据或趋势图没有数据，则不获取波形数据
        if (!hasTableData || !hasTrendData) {
          this.error = '暂无波形数据'
          this.data = []
          this.originalData = []
          return
        }

        const params = await getParams(1)

        // 检查 configNodeInfo 和 measureDefinitions 是否存在
        if (this.configNodeInfo &&
            this.configNodeInfo.measureDefinitions &&
            this.configNodeInfo.measureDefinitions.measureDefineName) {

          const measureDefineName = this.configNodeInfo.measureDefinitions.measureDefineName;
          console.log(this.configNodeInfo.measureDefinitions);

          if (measureDefineName.indexOf('包络') !== -1) {
            params.band = this.configNodeInfo.measureDefinitions.envelopeFrequencyDetail.split('-').map(Number)
            params.env_type = 'wave'
            params.ftype = 4
          }

          // 记录当前的测量类型
          this.dataFeatures.measureType = measureDefineName;
        }

        const result = await getWaveform(params)
        if (result.data && result.data.x && result.data.y) {
          this.originalData = result.data.x.map((xValue, index) => {
            return [xValue, result.data.y[index]]
          }).filter(point => point[0] != null && point[1] != null)

          this.data = [...this.originalData]

          // 重新分析数据特征
          this.analyzeDataFeatures();

          // 清除错误状态
          this.error = null

          return true;
        }
        this.error = '数据格式错误'
        return false;
      } catch (error) {
        console.error('获取数据失败:', error)
        this.error = '数据加载失败'
        // 清空数据
        this.data = []
        this.originalData = []
        // 重置游标
        this.showCursor1 = false
        this.cursor1 = null
        this.cursor2 = null
        return false;
      }
    },
    async checkDataStore() {
      const { device_id, point_id } = this.$store.state.dataStore
      if (!device_id || !point_id) {
        throw new Error('Missing required data store values')
      }
    },
    // 修改标切换方法
    toggleCursor(type) {
      if (type === 1) {
        this.showCursor1 = !this.showCursor1;
        if (this.showCursor1) {
          this.linkedSourceFrequency = null; // 清除联动频率标记
          // 如果有上次的位置，使用上次的位置
          if (this.lastCursorPositions.A.cursor1 !== null) {
            this.cursor1 = this.lastCursorPositions.A.cursor1;
            this.cursor2 = this.lastCursorPositions.A.cursor2;
            this.cursor1Y = this.lastCursorPositions.A.cursor1Y;
            this.cursor2Y = this.lastCursorPositions.A.cursor2Y;
            this.cursor1Index = this.lastCursorPositions.A.cursor1Index;
            this.cursor2Index = this.lastCursorPositions.A.cursor2Index;
          } else {
            // 如果没有上次的位置，使用默认位置
            if (this.data.length > 0) {
              const index1 = Math.floor(this.data.length / 4);
              const index2 = Math.floor(this.data.length * 3 / 4);
              if (this.data[index1]) {
                this.cursor1 = this.data[index1][0];
                this.cursor1Y = this.data[index1][1];
                this.cursor1Index = index1; // 保存索引
              }
              if (this.data[index2]) {
                this.cursor2 = this.data[index2][0];
                this.cursor2Y = this.data[index2][1];
                this.cursor2Index = index2; // 保存索引
              }
            }
          }

          // 当激活游标A时，让图表获取焦点
          this.$nextTick(() => {
            if (this.$refs.chart) {
              this.$refs.chart.focus();
            }
          });
        } else {
          // 保存当前位置
          this.lastCursorPositions.A = {
            cursor1: this.cursor1,
            cursor2: this.cursor2,
            cursor1Y: this.cursor1Y,
            cursor2Y: this.cursor2Y,
            cursor1Index: this.cursor1Index,
            cursor2Index: this.cursor2Index
          };
          // 清除当前游标
          this.cursor1 = null;
          this.cursor2 = null;
          this.cursor1Y = null;
          this.cursor2Y = null;
          this.cursor1Index = null;
          this.cursor2Index = null;
          this.freqDiff = 0;
          this.ampDiff = 0;
          //清除连接线和点
          this.chartInstance.setOption({
            graphic: []
          }, {
            replaceMerge: ['graphic']
          });
        }
      } else {
        this.showCursor2 = !this.showCursor2;
        if (this.showCursor2) {
          this.linkedSourceFrequency = null; // 清除联动频率标记 (针对B游标，以防未来有类似联动)
          // 如果有上次的位置，使用上次的位置
          if (this.lastCursorPositions.B.cursor1 !== null) {
            this.cursorB1 = this.lastCursorPositions.B.cursor1;
            this.cursorB2 = this.lastCursorPositions.B.cursor2;
            this.cursorB1Y = this.lastCursorPositions.B.cursor1Y;
            this.cursorB2Y = this.lastCursorPositions.B.cursor2Y;
            this.cursorB1Index = this.lastCursorPositions.B.cursor1Index;
            this.cursorB2Index = this.lastCursorPositions.B.cursor2Index;
          } else {
            // 如果没有上次的位置，使用默认位置
            if (this.data.length > 0) {
              const index1 = Math.floor(this.data.length / 4);
              const index2 = Math.floor(this.data.length * 3 / 4);
              if (this.data[index1]) {
                this.cursorB1 = this.data[index1][0];
                this.cursorB1Y = this.data[index1][1];
                this.cursorB1Index = index1; // 保存索引
              }
              if (this.data[index2]) {
                this.cursorB2 = this.data[index2][0];
                this.cursorB2Y = this.data[index2][1];
                this.cursorB2Index = index2; // 保存索引
              }
            }
          }

          // 当激活游标B时，让图表获取焦点
          this.$nextTick(() => {
            if (this.$refs.chart) {
              this.$refs.chart.focus();
            }
          });
        } else {
          // 保存当前位置
          this.lastCursorPositions.B = {
            cursor1: this.cursorB1,
            cursor2: this.cursorB2,
            cursor1Y: this.cursorB1Y,
            cursor2Y: this.cursorB2Y,
            cursor1Index: this.cursorB1Index,
            cursor2Index: this.cursorB2Index
          };
          // 清除当前游标
          this.cursorB1 = null;
          this.cursorB2 = null;
          this.cursorB1Y = null;
          this.cursorB2Y = null;
          this.cursorB1Index = null;
          this.cursorB2Index = null;
          this.freqDiffB = 0;
          this.ampDiffB = 0;
          //清除连接线和点
          this.chartInstance.setOption({
            graphic: []
          }, {
            replaceMerge: ['graphic']
          });
        }
      }

      // 用 requestAnimationFrame 确保在下一帧更新
      requestAnimationFrame(() => {
        this.updateChart();
        this.calculateDiff();
      });
    },
    // 添加计算差值的方法
    calculateDiff() {
      if (this.linkedSourceFrequency && this.linkedSourceFrequency > 0 && this.showCursor1) {
        // 联动激活：时间差是源频率的理论周期，源频率直接用于显示
        this.freqDiff = 1 / this.linkedSourceFrequency; // 数值
        // ampDiff 的计算保持不变，它依赖 cursor1Y, cursor2Y (当前在联动时为null)
        if (this.cursor1Y !== null && this.cursor2Y !== null) {
          this.ampDiff = Math.abs(this.cursor2Y - this.cursor1Y).toFixed(3);
        } else {
          this.ampDiff = 0; // Or '-'
        }
      } else if (this.showCursor1 && this.cursor1 !== null && this.cursor2 !== null) {
        // 手动模式或无联动：时间差是游标A1和A2的实际间隔
        this.freqDiff = Math.abs(this.cursor2 - this.cursor1); // 数值
        this.linkedSourceFrequency = null; // 清除源频率标记
        if (this.cursor1Y !== null && this.cursor2Y !== null) {
          this.ampDiff = Math.abs(this.cursor2Y - this.cursor1Y).toFixed(3);
        } else {
          this.ampDiff = 0;
        }
      } else {
        this.freqDiff = 0;
        this.ampDiff = 0;
        this.linkedSourceFrequency = null;
      }

      // 计算B游标的差值 (这部分逻辑保持不变，基于B1, B2的实际位置)
      if (this.showCursor2 && this.cursorB1 !== null && this.cursorB2 !== null) {
        this.freqDiffB = Math.abs(this.cursorB2 - this.cursorB1).toFixed(3);

        // 查找最接近游标位置的数据点
        const findYValue = (x) => {
          const point = this.findNearestDataPoint(x);
          return point ? point[1] : null;
        };

        this.cursorB1Y = findYValue(this.cursorB1);
        this.cursorB2Y = findYValue(this.cursorB2);

        if (this.cursorB1Y !== null && this.cursorB2Y !== null) {
          this.ampDiffB = Math.abs(this.cursorB2Y - this.cursorB1Y).toFixed(3);
        }
      } else {
        this.freqDiffB = 0;
        this.ampDiffB = 0;
        this.cursorB1Y = null;
        this.cursorB2Y = null;
      }
    },
    // 判断鼠标是否在游标近
    isNearCursor(mouseX, cursorX) {
      if (!this.chartInstance || cursorX === null) return false;
      const cursorPixel = this.chartInstance.convertToPixel({xAxisIndex: 0}, cursorX);
      return Math.abs(mouseX - cursorPixel) <= 15;
    },

    // 切换A游标周期线显示
    togglePeriodLinesA() {
      this.showPeriodLinesA = !this.showPeriodLinesA;
      this.updateChart();
    },

    // 切换B游标周期线显示
    togglePeriodLinesB() {
      this.showPeriodLinesB = !this.showPeriodLinesB;
      this.updateChart();
    },

    // 计算A游标的周期线
    calculatePeriodLinesA() {
      if (!this.showPeriodLinesA || !this.cursor1 || !this.cursor2) {
        return [];
      }

      const period = Math.abs(this.cursor2 - this.cursor1);
      if (period === 0) return [];

      try {
        const [startX, endX] = this.chartInstance.getModel().getComponent('xAxis', 0).axis.scale.getExtent();
        const [startY, endY] = this.chartInstance.getModel().getComponent('yAxis', 0).axis.scale.getExtent();

        const lines = [];

        // 从A1游标位置开始，向两边扩展
        let pos = this.cursor1;
        let count = 1;

        // 向右扩展
        while (pos <= endX) {
          pos = this.cursor1 + period * count;
          if (pos > endX) break;

          // 避免与游标重叠
          if (Math.abs(pos - this.cursor1) > period * 0.05 &&
              Math.abs(pos - this.cursor2) > period * 0.05) {
            lines.push([{
              coord: [pos, startY],
              lineStyle: {
                type: 'dashed',
                color: '#ff4d4f',
                width: 1,
                opacity: 0.6
              }
            }, {
              coord: [pos, endY],
              label: {
                show: true,
                position: 'start',
                color: '#ff4d4f',
                fontSize: 12
              }
            }]);
          }
          count++;
        }

        // 向左扩展
        pos = this.cursor1;
        count = -1;
        while (pos >= startX) {
          pos = this.cursor1 + period * count;
          if (pos < startX) break;

          // 避免与游标重叠
          if (Math.abs(pos - this.cursor1) > period * 0.05 &&
              Math.abs(pos - this.cursor2) > period * 0.05) {
            lines.push([{
              coord: [pos, startY],
              lineStyle: {
                type: 'dashed',
                color: '#ff4d4f',
                width: 1,
                opacity: 0.6
              }
            }, {
              coord: [pos, endY],
              label: {
                show: true,
                position: 'start',
                color: '#ff4d4f',
                fontSize: 12
              }
            }]);
          }
          count--;
        }

        return lines;
      } catch (error) {
        console.warn('计算A游标周期线时发生错误:', error);
        return [];
      }
    },

    // 计算B游标的周期线
    calculatePeriodLinesB() {
      if (!this.showPeriodLinesB || !this.cursorB1 || !this.cursorB2) {
        return [];
      }

      const period = Math.abs(this.cursorB2 - this.cursorB1);
      if (period === 0) return [];

      try {
        const [startX, endX] = this.chartInstance.getModel().getComponent('xAxis', 0).axis.scale.getExtent();
        const [startY, endY] = this.chartInstance.getModel().getComponent('yAxis', 0).axis.scale.getExtent();

        const lines = [];

        // 从B1游标位置开始，向两边扩展
        let pos = this.cursorB1;
        let count = 1;

        // 向右扩展
        while (pos <= endX) {
          pos = this.cursorB1 + period * count;
          if (pos > endX) break;

          // 避免与游标重叠
          if (Math.abs(pos - this.cursorB1) > period * 0.05 &&
              Math.abs(pos - this.cursorB2) > period * 0.05) {
            lines.push([{
              coord: [pos, startY],
              lineStyle: {
                type: 'dashed',
                color: '#52c41a',
                width: 1,
                opacity: 0.6
              }
            }, {
              coord: [pos, endY],
              label: {
                show: true,
                position: 'start',
                color: '#52c41a',
                fontSize: 12
              }
            }]);
          }
          count++;
        }

        // 向左扩展
        pos = this.cursorB1;
        count = -1;
        while (pos >= startX) {
          pos = this.cursorB1 + period * count;
          if (pos < startX) break;

          // 避免与游标重叠
          if (Math.abs(pos - this.cursorB1) > period * 0.05 &&
              Math.abs(pos - this.cursorB2) > period * 0.05) {
            lines.push([{
              coord: [pos, startY],
              lineStyle: {
                type: 'dashed',
                color: '#52c41a',
                width: 1,
                opacity: 0.6
              }
            }, {
              coord: [pos, endY],
              label: {
                show: true,
                position: 'start',
                color: '#52c41a',
                fontSize: 12
              }
            }]);
          }
          count--;
        }

        return lines;
      } catch (error) {
        console.warn('计算B游标周期线时发生错误:', error);
        return [];
      }
    },

    // 辅助方法：获取现有的标记线配置
    getExistingMarkLines() {
      const markLines = [];
      // 添加游标A的标记线
      if (this.showCursor1) {
        if (this.cursor1 !== null) {
          markLines.push({
            xAxis: this.cursor1,
            lineStyle: { color: 'red', width: 1, type: 'solid' },
            label: { formatter: `A1: ${this.cursor1.toFixed(3)}s` }
          });
        }
        if (this.cursor2 !== null) {
          markLines.push({
            xAxis: this.cursor2,
            lineStyle: { color: 'red', width: 1, type: 'solid' },
            label: { formatter: `A2: ${this.cursor2.toFixed(3)}s` }
          });
        }
      }
      // 添加游标B的标记线
      if (this.showCursor2) {
        // ... 游标B的标记线配置 ...
      }
      return markLines;
    },
    resetAxisRange() {
      // 重置为默认范围
      this.axisRange = JSON.parse(JSON.stringify(this.defaultAxisRange));
      this.applyAxisRange();
    },
    applyAxisRange() {
      if (this.chartInstance) {
        const option = {
          xAxis: {
            min: this.axisRange.xMin ? Number(this.axisRange.xMin) : null,
            max: this.axisRange.xMax ? Number(this.axisRange.xMax) : null
          },
          yAxis: {
            min: this.axisRange.yMin ? Number(this.axisRange.yMin) : null,
            max: this.axisRange.yMax ? Number(this.axisRange.yMax) : null
          }
        };

        this.chartInstance.setOption(option);
        this.showAxisRangeDialog = false;
      }
    },
    // 初始化默认范围
    initDefaultAxisRange() {
      if (this.data && this.data.length > 0) {
        // X轴默认范围
        this.defaultAxisRange.xMin = this.data[0][0];
        this.defaultAxisRange.xMax = this.data[this.data.length - 1][0];

        // Y轴默认范围
        const yValues = this.data.map(point => point[1]);
        this.defaultAxisRange.yMin = Math.min(...yValues);
        this.defaultAxisRange.yMax = Math.max(...yValues);

        // 初始化当前范围
        this.axisRange = JSON.parse(JSON.stringify(this.defaultAxisRange));
      }
    },
    // 处理 dataZoom 变化的方法
/*     handleDataZoomChange() {
      if (!this.currentDataZoom || !this.dataFeatures.ranges) return;

      const dataZoom = this.currentDataZoom[0];
      if (dataZoom) {
        const totalRange = this.dataFeatures.ranges.x.max - this.dataFeatures.ranges.x.min;

        // 将百分比转换为实际值
        const xMin = this.dataFeatures.ranges.x.min + (totalRange * dataZoom.start / 100);
        const xMax = this.dataFeatures.ranges.x.min + (totalRange * dataZoom.end / 100);

        // 更新axisRange
        this.axisRange = {
          ...this.axisRange,
          xMin: xMin.toFixed(6),
          xMax: xMax.toFixed(6)
        };

        // 更新图表
        this.chartInstance.setOption({
          xAxis: {
            min: xMin,
            max: xMax
          }
        }, {
          replaceMerge: ['xAxis']
        });
        this.updateChart()
      }
    }, */
    // 处理鼠标按下事件
    handleMouseDown(e) {
      // 如果是右键，让缩放mixin处理
      if (e.which === 3) return;

      if (!this.chartInstance) return;

      // 保存当前缩放状态
      const option = this.chartInstance.getOption();
      if (option.dataZoom && option.dataZoom.length > 0) {
        this.savedZoomState = {
          start: option.dataZoom[0].start,
          end: option.dataZoom[0].end
        };
      }

      // const rect = this.$refs.chart.getBoundingClientRect();
      const offsetX = e.offsetX;

      // 检查是否点击在游标A附近
      if (this.showCursor1) {
        if (this.isNearCursor(offsetX, this.cursor1)) {
          this.isDragging = true;
          this.activeCursor = 1;
          this.$refs.chart.style.cursor = 'ew-resize';
          return;
        } else if (this.isNearCursor(offsetX, this.cursor2)) {
          this.isDragging = true;
          this.activeCursor = 2;
          this.$refs.chart.style.cursor = 'ew-resize';
          return;
        }
      }

      // 检查是否点击在游标B附近
      if (this.showCursor2) {
        if (this.isNearCursor(offsetX, this.cursorB1)) {
          this.isDragging = true;
          this.activeCursor = 'B1';
          this.$refs.chart.style.cursor = 'ew-resize';
          return;
        } else if (this.isNearCursor(offsetX, this.cursorB2)) {
          this.isDragging = true;
          this.activeCursor = 'B2';
          this.$refs.chart.style.cursor = 'ew-resize';
          return;
        }
      }
    },

    handleMouseMove(e) {
      if (!this.chartInstance) return; // 如果图表实例不存在，则提前返回

      if (this.isDragging) { // 仅在拖动游标时处理
        this.linkedSourceFrequency = null; // 清除联动频率标记
      } else {
        return; // 如果不是拖动状态，则不执行后续逻辑
      }

      // 保存当前缩放状态
      const option = this.chartInstance.getOption();
      if (option.dataZoom && option.dataZoom.length > 0) {
        this.savedZoomState = {
          start: option.dataZoom[0].start,
          end: option.dataZoom[0].end
        };
      }

      const offsetX = e.offsetX;
      const currentX = this.chartInstance.convertFromPixel({xAxisIndex: 0}, offsetX);
      const maxX = this.data[this.data.length - 1][0];

      if (currentX >= 0 && currentX <= maxX) {
        const nearestPoint = this.findNearestDataPoint(currentX);
        if (nearestPoint) {
          // 找到最近点的索引
          const pointIndex = this.findIndexOfPoint(nearestPoint);

          // 立即更新游标位置和索引
          switch (this.activeCursor) {
            case 1:
              this.cursor1 = nearestPoint[0];
              this.cursor1Y = nearestPoint[1];
              this.cursor1Index = pointIndex; // 更新索引
              break;
            case 2:
              this.cursor2 = nearestPoint[0];
              this.cursor2Y = nearestPoint[1];
              this.cursor2Index = pointIndex; // 更新索引
              break;
            case 'B1':
              this.cursorB1 = nearestPoint[0];
              this.cursorB1Y = nearestPoint[1];
              this.cursorB1Index = pointIndex; // 更新索引
              break;
            case 'B2':
              this.cursorB2 = nearestPoint[0];
              this.cursorB2Y = nearestPoint[1];
              this.cursorB2Index = pointIndex; // 更新索引
              break;
          }

          // 立即计算差值
          this.calculateDiff();

          // 立即更新图表
          this.updateChart();
        }
      }
    },

    handleMouseUp() {
      if (this.isDragging) {
        this.isDragging = false;
        this.activeCursor = null;
        this.$refs.chart.style.cursor = 'default';
      }
      if (this.isDraggingIntervalB) {
        this.isDraggingIntervalB = false;
      }
    },

    findNearestDataPoint(xValue) {
      if (!this.data || this.data.length === 0) return null;

      // 使用二分查找找到最近的点
      let left = 0;
      let right = this.data.length - 1;

      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const midX = this.data[mid][0];

        if (midX === xValue) {
          return this.data[mid];
        }

        if (midX < xValue) {
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }

      // 比较左右两个点，找出最近的点
      const leftPoint = this.data[Math.max(0, right)];
      const rightPoint = this.data[Math.min(this.data.length - 1, left)];

      if (!leftPoint) return rightPoint;
      if (!rightPoint) return leftPoint;

      return Math.abs(leftPoint[0] - xValue) <= Math.abs(rightPoint[0] - xValue)
        ? leftPoint
        : rightPoint;
    },
    // 添加键盘事件处理方法
    handleKeydown(event) {
      // 添加调试信息
      console.log('键盘事件触发:', event.key, '修饰键:', event.ctrlKey ? 'Ctrl' : '', event.shiftKey ? 'Shift' : '');

      if (event.key !== 'ArrowLeft' && event.key !== 'ArrowRight') return;

      // 当B游标显示时，优先处理B游标
      if (this.showCursor2) {
        console.log('B游标状态:', this.showCursor2, 'B1索引:', this.cursorB1Index, 'B1值:', this.cursorB1);

        if (event.ctrlKey) {  // 移动B1
          console.log('尝试移动B1游标');
          if (this.cursorB1 !== null && this.cursorB1Index !== null) {
            // 直接使用保存的索引
            const currentIndex = this.cursorB1Index;
            console.log('B1当前索引:', currentIndex);

            // 计算目标索引
            const targetIndex = event.key === 'ArrowLeft' ?
              Math.max(0, currentIndex - 1) :
              Math.min(this.data.length - 1, currentIndex + 1);
            console.log('B1目标索引:', targetIndex);

            // 获取目标点
            const targetPoint = this.data[targetIndex];
            console.log('B1目标点:', targetPoint);

            // 更新游标位置和索引
            this.cursorB1 = targetPoint[0];
            this.cursorB1Y = targetPoint[1];
            this.cursorB1Index = targetIndex; // 更新索引

            this.calculateDiff();
            this.updateChart();
            console.log('B1游标已更新到索引', targetIndex);
            event.preventDefault();
          } else {
            console.log('B1游标或索引为空，无法移动');
          }
        } else if (event.shiftKey) {  // 移动B2
          console.log('尝试移动B2游标');
          if (this.cursorB2 !== null && this.cursorB2Index !== null) {
            // 直接使用保存的索引
            const currentIndex = this.cursorB2Index;
            // 计算目标索引
            const targetIndex = event.key === 'ArrowLeft' ?
              Math.max(0, currentIndex - 1) :
              Math.min(this.data.length - 1, currentIndex + 1);

            // 获取目标点
            const targetPoint = this.data[targetIndex];

            // 确保B2不会移到B1左边
            if (event.key === 'ArrowLeft' && targetPoint[0] <= this.cursorB1) {
              return;
            }

            // 更新游标位置和索引
            this.cursorB2 = targetPoint[0];
            this.cursorB2Y = targetPoint[1];
            this.cursorB2Index = targetIndex; // 更新索引

            this.calculateDiff();
            this.updateChart();
            console.log('B2当前索引为', targetIndex);
            event.preventDefault();
          }
        }
      }
      // 当只有A游标显示时，处理A游标
      else if (this.showCursor1) {
        console.log('A游标状态:', this.showCursor1, 'A1索引:', this.cursor1Index, 'A1值:', this.cursor1);

        if (event.ctrlKey) {  // 移动A1
          console.log('尝试移动A1游标');
          if (this.cursor1 !== null && this.cursor1Index !== null) {
            // 直接使用保存的索引
            const currentIndex = this.cursor1Index;
            console.log('A1当前索引:', currentIndex);

            // 计算目标索引
            const targetIndex = event.key === 'ArrowLeft' ?
              Math.max(0, currentIndex - 1) :
              Math.min(this.data.length - 1, currentIndex + 1);
            console.log('A1目标索引:', targetIndex);

            // 获取目标点
            const targetPoint = this.data[targetIndex];
            console.log('A1目标点:', targetPoint);

            // 更新游标位置和索引
            this.cursor1 = targetPoint[0];
            this.cursor1Y = targetPoint[1];
            this.cursor1Index = targetIndex; // 更新索引

            this.calculateDiff();
            this.updateChart();
            console.log('A1游标已更新到索引', targetIndex);
            event.preventDefault();
          } else {
            console.log('A1游标或索引为空，无法移动');
          }
        } else if (event.shiftKey) {  // 移动A2
          console.log('尝试移动A2游标');
          if (this.cursor2 !== null && this.cursor2Index !== null) {
            // 直接使用保存的索引
            const currentIndex = this.cursor2Index;
            // 计算目标索引
            const targetIndex = event.key === 'ArrowLeft' ?
              Math.max(0, currentIndex - 1) :
              Math.min(this.data.length - 1, currentIndex + 1);

            // 获取目标点
            const targetPoint = this.data[targetIndex];

            // 确保A2不会移到A1左边
            if (event.key === 'ArrowLeft' && targetPoint[0] <= this.cursor1) {
              return;
            }

            // 更新游标位置和索引
            this.cursor2 = targetPoint[0];
            this.cursor2Y = targetPoint[1];
            this.cursor2Index = targetIndex; // 更新索引

            this.calculateDiff();
            this.updateChart();
            console.log('A2当前索引为', targetIndex);
            event.preventDefault();
          }
        }
      } else {
        console.log('没有激活的游标');
      }
    },
    analyzeDataFeatures() {
      if(this.data && this.data.length) {
        const xValues = this.data.map(point => point[0])
        const yValues = this.data.map(point => point[1])

        // 计算数据范围
        this.dataFeatures.ranges = {
          x: {
            min: Math.min(...xValues),
            max: Math.max(...xValues)
          },
          y: {
            min: Math.min(...yValues),
            max: Math.max(...yValues)
          }
        }

        // 计算数据密度
        const xRange = this.dataFeatures.ranges.x.max - this.dataFeatures.ranges.x.min
        this.dataFeatures.density = {
          pointsPerUnit: this.data.length / xRange,
          averageSpacing: xRange / (this.data.length - 1)
        }

        // 记录测量类型
        this.dataFeatures.measureType = this.configNodeInfo?.measureDefinitions?.measureDefineName || null
      }
    },

    generateZoomConfig() {
      // 如果存在保存的缩放状态，优先使用保存的状态
      if (this.savedZoomState) {
        return [{
          type: 'inside',
          start: this.savedZoomState.start,
          end: this.savedZoomState.end,
          zoomOnMouseWheel: true,
          moveOnMouseMove: false, // 禁用鼠标拖动
          moveOnMouseWheel: false, // 禁用鼠标滚轮拖动
          preventDefaultMouseMove: true, // 阻止默认的鼠标移动行为
          zoomLock: false
        }]
      }

      const {density, ranges} = this.dataFeatures
      const currentZoom = this.currentDataZoom?.[0] || { start: 0, end: 100 }

      // 计算实际的数据范围
      const totalRange = ranges.x.max - ranges.x.min

      // 如果有设置了axisRange，使用axisRange的值
      const xMin = this.axisRange.xMin !== null ? Number(this.axisRange.xMin) : ranges.x.min
      const xMax = this.axisRange.xMax !== null ? Number(this.axisRange.xMax) : ranges.x.max

      // 计算当前视图范围相对于总范围的百分比
      const start = ((xMin - ranges.x.min) / totalRange) * 100
      const end = ((xMax - ranges.x.min) / totalRange) * 100

      // 根据数据密度调整最小缩放范围
      const minSpan = Math.max(1, 100 / (density.pointsPerUnit * ranges.x.max))

      return [{
        type: 'inside',
        start: start,
        end: end,
        minSpan,
        maxSpan: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: false, // 禁用鼠标拖动
        moveOnMouseWheel: false, // 禁用鼠标滚轮拖动
        preventDefaultMouseMove: true, // 阻止默认的鼠标移动行为
        zoomLock: false
      }]
    },

    adjustZoomForNewData() {
      if (!this.chartInstance || !this.data.length) return

      // 分析新数据特征
      this.analyzeDataFeatures()

      // 生成新的缩放配置
      const zoomConfig = this.generateZoomConfig()

      // 更新图表配置
      const option = this.chartInstance.getOption()
      option.dataZoom = zoomConfig
      this.chartInstance.setOption(option)
    },

    handleDataZoomChange() {
      if (this.chartInstance) {
        const option = this.chartInstance.getOption();
        if (option.dataZoom && option.dataZoom.length > 0) {
          this.savedZoomState = {
            start: option.dataZoom[0].start,
            end: option.dataZoom[0].end
          };
          // 在缩放后更新图表
          this.updateChart()
        }
      }
    },

    handleZoomFromMixin() {
      // 当 mixin 中的缩放操作完成时，确保图表（包括连接线）得到更新
      if (this.chartInstance) {
        // 首先，确保 currentDataZoom 和 savedZoomState 与图表实例的 dataZoom 同步
        const option = this.chartInstance.getOption();
        if (option.dataZoom && option.dataZoom.length > 0) {
          this.currentDataZoom = option.dataZoom; // 更新 currentDataZoom
          this.savedZoomState = { // 更新 savedZoomState
            start: option.dataZoom[0].start,
            end: option.dataZoom[0].end
          };
        }

        // 然后调用 updateChart 来重绘所有元素
        this.updateChart();
      }
    },

    // 添加查找数据点索引的方法
    findIndexOfPoint(point) {
      if (!point || !this.data || this.data.length === 0) return -1;

      // 使用二分查找找到最接近的点的索引
      let left = 0;
      let right = this.data.length - 1;

      // 处理边界情况
      if (point[0] <= this.data[0][0]) return 0;
      if (point[0] >= this.data[right][0]) return right;

      // 二分查找
      while (right - left > 1) {
        const mid = Math.floor((left + right) / 2);
        if (this.data[mid][0] < point[0]) {
          left = mid;
        } else {
          right = mid;
        }
      }

      // 返回最接近的点的索引
      return Math.abs(this.data[left][0] - point[0]) <= Math.abs(this.data[right][0] - point[0])
        ? left : right;
    },
    // 鼠标离开图表时的处理
    handleChartMouseLeave() {
      this.isChartFocused = false;
    },

    // 鼠标进入图表时的处理
    handleChartMouseEnter() {
      this.isChartFocused = true;
    },

    // 处理普通左箭头键事件
    handleLeftArrow() {
      console.log('处理左箭头键事件');
      // 无修饰键时的默认移动逻辑
      this.moveFirstActiveCursor(-1);
    },

    // 处理普通右箭头键事件
    handleRightArrow() {
      console.log('处理右箭头键事件');
      // 无修饰键时的默认移动逻辑
      this.moveFirstActiveCursor(1);
    },

    // 处理Ctrl+左箭头键事件，移动第一游标
    handleCtrlLeftArrow() {
      console.log('处理Ctrl+左箭头键事件');
      if (this.showCursor2) { // B游标模式
        this.moveCursorB1(-1);
      } else if (this.showCursor1) { // A游标模式
        this.moveCursor1(-1);
      }
    },

    // 处理Ctrl+右箭头键事件，移动第一游标
    handleCtrlRightArrow() {
      console.log('处理Ctrl+右箭头键事件');
      if (this.showCursor2) { // B游标模式
        this.moveCursorB1(1);
      } else if (this.showCursor1) { // A游标模式
        this.moveCursor1(1);
      }
    },

    // 处理Shift+左箭头键事件，移动第二游标
    handleShiftLeftArrow() {
      console.log('处理Shift+左箭头键事件');
      if (this.showCursor2) { // B游标模式
        this.moveCursorB2(-1);
      } else if (this.showCursor1) { // A游标模式
        this.moveCursor2(-1);
      }
    },

    // 处理Shift+右箭头键事件，移动第二游标
    handleShiftRightArrow() {
      console.log('处理Shift+右箭头键事件');
      if (this.showCursor2) { // B游标模式
        this.moveCursorB2(1);
      } else if (this.showCursor1) { // A游标模式
        this.moveCursor2(1);
      }
    },

    // 移动当前活动的第一游标（无修饰键时）
    moveFirstActiveCursor(direction) {
      if (this.showCursor2) { // B游标模式
        this.moveCursorB1(direction);
      } else if (this.showCursor1) { // A游标模式
        this.moveCursor1(direction);
      }
    },

    // 移动A1游标
    moveCursor1(direction) {
      this.linkedSourceFrequency = null; // 清除联动频率标记
      if (this.cursor1 === null || this.cursor1Index === null) return;

      // 使用保存的游标索引
      const currentIndex = this.cursor1Index;

      // 计算新索引
      const targetIndex = direction > 0
        ? Math.min(this.data.length - 1, currentIndex + 1)
        : Math.max(0, currentIndex - 1);

      if (targetIndex === currentIndex) return;

      // 获取目标点
      const targetPoint = this.data[targetIndex];

      // 更新游标位置和索引
      this.cursor1 = targetPoint[0];
      this.cursor1Y = targetPoint[1];
      this.cursor1Index = targetIndex;

      this.calculateDiff();
      this.updateChart();
    },

    // 移动A2游标
    moveCursor2(direction) {
      this.linkedSourceFrequency = null; // 清除联动频率标记
      if (this.cursor2 === null || this.cursor2Index === null) return;

      // 使用保存的游标索引
      const currentIndex = this.cursor2Index;

      // 计算新索引
      const targetIndex = direction > 0
        ? Math.min(this.data.length - 1, currentIndex + 1)
        : Math.max(0, currentIndex - 1);

      if (targetIndex === currentIndex) return;

      // 获取目标点
      const targetPoint = this.data[targetIndex];

      // 如果向左移动，确保不会越过A1游标
      if (direction < 0 && this.cursor1 !== null && targetPoint[0] <= this.cursor1) return;

      // 更新游标位置和索引
      this.cursor2 = targetPoint[0];
      this.cursor2Y = targetPoint[1];
      this.cursor2Index = targetIndex;

      this.calculateDiff();
      this.updateChart();
    },

    // 移动B1游标
    moveCursorB1(direction) {
      this.linkedSourceFrequency = null; // 清除联动频率标记
      if (this.cursorB1 === null || this.cursorB1Index === null) return;

      // 使用保存的游标索引
      const currentIndex = this.cursorB1Index;

      // 计算新索引
      const targetIndex = direction > 0
        ? Math.min(this.data.length - 1, currentIndex + 1)
        : Math.max(0, currentIndex - 1);

      if (targetIndex === currentIndex) return;

      // 获取目标点
      const targetPoint = this.data[targetIndex];

      // 更新游标位置和索引
      this.cursorB1 = targetPoint[0];
      this.cursorB1Y = targetPoint[1];
      this.cursorB1Index = targetIndex;

      this.calculateDiff();
      this.updateChart();
    },

    // 移动B2游标
    moveCursorB2(direction) {
      this.linkedSourceFrequency = null; // 清除联动频率标记
      if (this.cursorB2 === null || this.cursorB2Index === null) return;

      // 使用保存的游标索引
      const currentIndex = this.cursorB2Index;

      // 计算新索引
      const targetIndex = direction > 0
        ? Math.min(this.data.length - 1, currentIndex + 1)
        : Math.max(0, currentIndex - 1);

      if (targetIndex === currentIndex) return;

      // 获取目标点
      const targetPoint = this.data[targetIndex];

      // 如果向左移动，确保不会越过B1游标
      if (direction < 0 && this.cursorB1 !== null && targetPoint[0] <= this.cursorB1) return;

      // 更新游标位置和索引
      this.cursorB2 = targetPoint[0];
      this.cursorB2Y = targetPoint[1];
      this.cursorB2Index = targetIndex;

      this.calculateDiff();
      this.updateChart();
    },
    handlePeriodLineDragEnd(e) {
      document.removeEventListener('mousemove', this.handlePeriodLineDrag);
      document.removeEventListener('mouseup', this.handlePeriodLineMouseUp);
      this.isDraggingPeriodLine = false;
      this.updateChart();
    },
  }
}
</script>

<style scoped lang="scss">
.sycontainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.spectrum-toolbar {
  padding: 0 10px;
  background: #f5f5f5;
  height: 30px;
  display: flex;
  align-items: center;
  gap: 10px;

  .fuction {
    display: flex;
    gap: 8px;

    .cursor-btn {
      min-width: 32px;
      height: 24px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: white;
      color: #595959;
      font-size: 13px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      padding: 0 8px;

      &:hover {
        background: #f0f0f0;
        border-color: #40a9ff;
        color: #40a9ff;
      }

      &.active {
        background: #1890ff;
        border-color: #1890ff;
        color: white;
      }

      &:active {
        transform: scale(0.98);
      }

      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #bfbfbf;
        cursor: not-allowed;

        &:hover {
          background: #f5f5f5;
          border-color: #d9d9d9;
          color: #bfbfbf;
        }
      }
    }
  }

  .display-data {
    display: flex;
    gap: 16px;
    align-items: center;
    font-size: 13px;
    color: #595959;

    div {
      white-space: nowrap;
    }
  }
}

.chart {
  flex: 1;
  width: 100%;
  height: calc(100% - 60px);
  box-sizing: border-box;
  padding: 5px;
  background-color: #f5f5f5;
  position: relative;
  outline: none; /* 防止获取焦点时出现默认轮廓 */
}

.top-container {
  width: 100%;
  height: 30px;
  background-color: #f5f5f5;
  font-size: 14px;
  text-align: left;
  display: flex;
  padding: 3px;
  /* justify-content: center;
  align-items: center; */
}

.no-data-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
}

.btn{
  border: none;
}
.btn:active{
  transform:scale(0.9) ;
}

.axis-range-form {
  padding: 10px;

  .axis-group {
    margin-bottom: 15px;

    .axis-label {
      margin-bottom: 8px;
      font-weight: bold;
    }

    .el-input {
      width: 45%;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.axis-range-dialog {
  .axis-range-form {
    padding: 10px;

    .axis-group {
      margin-bottom: 20px;

      .axis-label {
        margin-bottom: 10px;
        font-weight: bold;
        color: #606266;
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

/* 修改深度选择器的写法 */
:deep(.el-input-group__prepend) {
  padding: 0 10px;
}

:deep(.el-input__inner) {
  text-align: center;
}

/* 键盘指南对话框样式 */
.keyboard-help {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin-left: 8px;

  .help-icon {
    font-size: 16px;
    color: #1890ff;
    cursor: help;
    user-select: none;
    transition: color 0.3s;

    &:hover {
      color: #40a9ff;
      & + .help-content {
        visibility: visible;
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .help-content {
    position: absolute;
    top: 100%;
    right: 0;
    width: 280px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 12px;
    visibility: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;

    h4 {
      color: #1890ff;
      margin-bottom: 8px;
      font-size: 13px;
    }

    ul {
      list-style: none;
      padding-left: 12px;
      margin-bottom: 12px;

      li {
        margin-bottom: 6px;
        color: #595959;
        font-size: 12px;

        &:before {
          content: "•";
          color: #1890ff;
          margin-right: 6px;
        }
      }
    }

    .note {
      color: #ff4d4f;
      font-size: 11px;
      margin-top: 8px;
      padding-left: 12px;
    }

    &:before {
      content: '';
      position: absolute;
      top: -4px;
      right: 10px;
      width: 8px;
      height: 8px;
      background: white;
      transform: rotate(45deg);
      box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.05);
    }
  }
}
</style>
